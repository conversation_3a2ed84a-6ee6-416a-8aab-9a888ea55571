import { supabase } from "@/lib/supabase";
import { useAuthStore } from "@/stores/authStore";
import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import * as WebBrowser from "expo-web-browser";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

// Required for web browser authentication completion
WebBrowser.maybeCompleteAuthSession();

const VerifyUser = () => {
  const params = useLocalSearchParams();
  const email = params.email as string;
  const isNewUser = params.isNewUser === "true";
  const { checkProfileComplete } = useAuthStore();

  const [loading, setLoading] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<
    "idle" | "verifying" | "success" | "error"
  >("idle");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(30);

  const createUserProfile = async (userId: string, email: string) => {
    try {
      // Use the new initialization function that includes all required fields
      const success = await initializeNewProfile(userId, email);

      if (!success) {
        console.error("Error creating profile with new initialization");
        // Fallback to old method if new one fails
        const { error } = await supabase.from("profiles").insert({
          id: userId,
          username: email.split("@")[0],
          full_name: null,
          avatar_url: null,
          gender: null,
          age: null,
          about: null,
          is_first_time: true,
          location_lat: null,
          location_lng: null,
          notification_enabled: false,
          sexual_orientation: null,
          looking_for: null,
          last_active_at: new Date().toISOString(),
          profile_complete: false,
          visibility_status: "public",
          updated_at: new Date().toISOString(),
        });

        if (error && error.code !== "23505") {
          // Ignore duplicate key error
          console.error("Error creating profile with fallback method:", error);
        }
      }
    } catch (error) {
      console.error("Error in createUserProfile:", error);
    }
  };

  // Handle OTP input change
  const handleOtpChange = (text: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Auto-focus next input field if text is entered
    if (text && index < 5) {
      // Would need refs for TextInputs to implement true auto-focus
      // This is simplified for the code example
    }
  };

  // Verify OTP
  const verifyOtp = async () => {
    const otpValue = otp.join("");
    if (otpValue.length !== 6) {
      Alert.alert("Error", "Please enter the complete 6-digit OTP");
      return;
    }

    setVerificationStatus("verifying");
    setLoading(true);

    try {
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token: otpValue,
        type: isNewUser ? "signup" : "email",
      });

      if (error) throw error;

      // Create profile for new users
      if (isNewUser && data.user) {
        await createUserProfile(data.user.id, email);
      }

      setVerificationStatus("success");

      // Check profile completion and redirect accordingly
      setTimeout(async () => {
        if (data.user) {
          await checkProfileComplete();
        }
        router.replace("/(onboarding)/profile-setup");
      }, 1500);
    } catch (error: any) {
      setVerificationStatus("error");
      setErrorMessage(error.message || "Invalid or expired OTP");
    } finally {
      setLoading(false);
    }
  };

  // Resend OTP
  const resendOtp = async () => {
    setResendDisabled(true);
    setCountdown(30);

    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: isNewUser,
        },
      });

      if (error) {
        Alert.alert("Error", error.message);
      } else {
        Alert.alert("OTP Sent", "A new OTP has been sent to your email");
      }
    } catch (error: any) {
      Alert.alert("Error", error.message || "Failed to resend OTP");
    }
  };

  // Countdown timer for resend button
  useEffect(() => {
    let timerId: ReturnType<typeof setInterval>;

    if (resendDisabled && countdown > 0) {
      timerId = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerId) clearInterval(timerId);
    };
  }, [resendDisabled, countdown]);

  // Helper function to handle social login
  const performOAuth = async (provider: "github" | "google") => {
    try {
      setLoading(true);
      setVerificationStatus("verifying");

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: "pyarwalaapp://auth",
          skipBrowserRedirect: true,
        },
      });

      if (error) throw error;
      if (!data?.url) throw new Error("No authentication URL returned");

      const res = await WebBrowser.openAuthSessionAsync(
        data.url,
        "pyarwalaapp://auth"
      );

      if (res.type === "success") {
        setVerificationStatus("success");

        // After a short delay, redirect
        setTimeout(() => {
          router.replace("/(onboarding)/profile-setup");
        }, 1500);
      } else {
        setVerificationStatus("idle");
      }
    } catch (error: any) {
      setVerificationStatus("error");
      setErrorMessage(error.message || "Authentication failed");
    } finally {
      setLoading(false);
    }
  };

  // Render different UI based on verification status
  const renderContent = () => {
    switch (verificationStatus) {
      case "verifying":
        return (
          <View className="flex-1 justify-center items-center p-5">
            <ActivityIndicator size="large" color="#E94057" />
            <Text className="text-2xl font-bold mt-5 text-center">
              Verifying your code...
            </Text>
          </View>
        );

      case "success":
        return (
          <View className="flex-1 justify-center items-center p-5">
            <Ionicons name="checkmark-circle" size={80} color="#4CAF50" />
            <Text className="text-2xl font-bold mt-5 text-center">
              Verification Successful!
            </Text>
            <Text className="text-base text-gray-500 mt-3 mb-8 text-center">
              Redirecting you to the app...
            </Text>
          </View>
        );

      case "error":
        return (
          <View className="flex-1 justify-center items-center p-5">
            <Ionicons name="alert-circle" size={80} color="#E94057" />
            <Text className="text-2xl font-bold mt-5 text-center">
              Verification Failed
            </Text>
            {errorMessage && (
              <Text className="text-sm text-red-500 mt-3 mb-5 text-center">
                {errorMessage}
              </Text>
            )}
            <TouchableOpacity
              className="bg-[#E94057] py-4 px-8 rounded-full mt-5"
              onPress={() => setVerificationStatus("idle")}
            >
              <Text className="text-white font-bold text-base">Try Again</Text>
            </TouchableOpacity>
          </View>
        );

      default: // idle
        return (
          <View className="flex-1 p-5 justify-center">
            <View className="items-center mb-10">
              <Text className="text-3xl font-bold text-gray-800 mb-3">
                Enter Verification Code
              </Text>
              <Text className="text-base text-gray-600 text-center">
                We've sent a 6-digit code to{"\n"}
                {email}
              </Text>
            </View>

            {/* OTP Input */}
            <View className="flex-row justify-between mb-10">
              {otp.map((digit, index) => (
                <TextInput
                  key={index}
                  className="bg-white h-14 w-14 border border-gray-200 rounded-lg text-center text-xl font-bold shadow-sm"
                  keyboardType="number-pad"
                  maxLength={1}
                  value={digit}
                  onChangeText={(text) => handleOtpChange(text, index)}
                />
              ))}
            </View>

            {/* Verify Button */}
            <TouchableOpacity
              className="bg-[#E94057] py-4 rounded-xl mb-6"
              onPress={verifyOtp}
              disabled={loading || otp.join("").length !== 6}
              style={{
                opacity: loading || otp.join("").length !== 6 ? 0.7 : 1,
              }}
            >
              <Text className="text-white text-center text-lg font-semibold">
                Verify
              </Text>
            </TouchableOpacity>

            {/* Resend OTP */}
            <View className="flex-row justify-center items-center mb-10">
              <Text className="text-gray-600">Didn't receive the code? </Text>
              {resendDisabled ? (
                <Text className="text-[#E94057] font-medium">
                  Resend in {countdown}s
                </Text>
              ) : (
                <TouchableOpacity onPress={resendOtp}>
                  <Text className="text-[#E94057] font-medium">Resend</Text>
                </TouchableOpacity>
              )}
            </View>

            <View className="flex-row items-center my-5">
              <View className="flex-1 h-px bg-gray-200" />
              <Text className="mx-4 text-gray-500">or</Text>
              <View className="flex-1 h-px bg-gray-200" />
            </View>

            {/* Social Login Options */}
            <View className="w-full mb-8">
              <TouchableOpacity
                className="flex-row items-center justify-center bg-[#4285F4] p-4 rounded-xl mb-4"
                onPress={() => performOAuth("google")}
                disabled={loading}
              >
                <Ionicons name="logo-google" size={24} color="#fff" />
                <Text className="text-white text-base font-semibold ml-3">
                  Continue with Google
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="items-center mt-4"
                onPress={() => router.back()}
              >
                <Text className="text-[#E94057] text-base font-medium">
                  Go Back
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        );
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      {renderContent()}
    </SafeAreaView>
  );
};

export default VerifyUser;
