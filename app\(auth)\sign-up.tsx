import SignUpCom from "@/components/auth/sign-up";
import { initializeNewProfile } from "@/lib/databaseMigration";
import { supabase } from "@/lib/supabase";
import { toast } from "@/lib/toast";
import { useAuthStore } from "@/stores/authStore";
import { router } from "expo-router";
import React, { useState } from "react";
import { Platform, View } from "react-native";

const SignUp = () => {
  const [loading, setLoading] = useState(false);
  const { checkProfileComplete } = useAuthStore();

  const createUserProfile = async (userId: string, email: string) => {
    try {
      // Use the new initialization function that includes all required fields
      const success = await initializeNewProfile(userId, email);

      if (!success) {
        console.error("Error creating profile with new initialization");
        // Fallback to old method if new one fails
        const { error } = await supabase.from("profiles").insert({
          id: userId,
          username: email.split("@")[0],
          full_name: null,
          avatar_url: null,
          gender: null,
          age: null,
          about: null,
          is_first_time: true,
          location_lat: null,
          location_lng: null,
          notification_enabled: false,
          sexual_orientation: null,
          looking_for: null,
          last_active_at: new Date().toISOString(),
          profile_complete: false,
          visibility_status: "public",
          updated_at: new Date().toISOString(),
        });

        if (error) {
          console.error("Error creating profile with fallback method:", error);
        }
      }
    } catch (error) {
      console.error("Error in createUserProfile:", error);
    }
  };

  const handleSignUp = async (email: string) => {
    if (!email) {
      toast.error("Please provide your email address");
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email: email,
        options: {
          shouldCreateUser: true,
          emailRedirectTo:
            Platform.OS === "web" ? window.location.origin : undefined,
        },
      });

      if (error) {
        toast.error(error.message);
      } else {
        toast.success(
          "Check your email! We've sent a one-time password to your email address"
        );
        router.push({
          pathname: "/(auth)/verify-user",
          params: { email, isNewUser: "true" },
        });
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to sign up");
      console.error("Sign up error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = async (provider: string) => {
    if (provider !== "google") return;

    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: provider as "google",
        options: {
          redirectTo:
            Platform.OS === "web"
              ? window.location.origin
              : "pyarwalaapp://auth",
        },
      });

      if (error) {
        toast.error(error.message);
      } else {
        // Profile creation will be handled by auth state change listener
        toast.info("Redirecting to Google...");
        console.log("Google OAuth initiated successfully");
      }
    } catch (error: any) {
      toast.error("Failed to sign up with Google");
      console.error("Google sign up error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View className="flex-1">
      <SignUpCom
        onSignUp={handleSignUp}
        onSocialLogin={handleSocialLogin}
        loading={loading}
      />
    </View>
  );
};

export default SignUp;
