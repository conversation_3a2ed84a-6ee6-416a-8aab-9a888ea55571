import { Ionicons } from "@expo/vector-icons";
import { BlurView } from "expo-blur";
import { Tabs } from "expo-router";
import React from "react";
import { Platform, View } from "react-native";

const TabLayout = () => {
  return (
    <Tabs
      screenOptions={{
        tabBarStyle: {
          position: "absolute",
          bottom: 25,
          left: 20,
          right: 20,
          elevation: 0,
          backgroundColor: "transparent",
          borderRadius: 25,
          height: 70,
          borderTopWidth: 0,
          borderColor: "rgba(255, 255, 255, 0.18)",
          borderWidth: Platform.OS === "ios" ? 0.3 : 0,
          shadowColor: "#000",
          shadowOffset: {
            width: 0,
            height: 10,
          },
          shadowOpacity: 0.25,
          shadowRadius: 13,
        },
        tabBarBackground: () => (
          <BlurView
            tint="light"
            intensity={Platform.OS === "ios" ? 50 : 70}
            className="absolute inset-0 rounded-[25px] overflow-hidden"
            style={{ backgroundColor: "#FECFEF40" }}
          />
        ),
        tabBarActiveTintColor: "#E94057",
        tabBarInactiveTintColor: "rgba(0, 0, 0, 0.5)",
        headerShown: false,
        tabBarShowLabel: false,
        tabBarItemStyle: {
          paddingTop: 10,
          paddingBottom: 10,
        },
      }}
    >
      <Tabs.Screen
        name="main"
        options={{
          tabBarIcon: ({ color, size, focused }) => (
            <View
              className={
                focused
                  ? "bg-[rgba(233,64,87,0.15)] p-3 rounded-full shadow-sm"
                  : "p-3 rounded-full"
              }
            >
              <Ionicons name="flame" size={size} color={color} />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="matches"
        options={{
          tabBarIcon: ({ color, size, focused }) => (
            <View
              className={
                focused
                  ? "bg-[rgba(233,64,87,0.15)] p-3 rounded-full shadow-sm"
                  : "p-3 rounded-full"
              }
            >
              <Ionicons name="heart" size={size} color={color} />
            </View>
          ),
          tabBarBadge: 3,
          tabBarBadgeStyle: {
            backgroundColor: "#E94057",
            fontSize: 10,
            fontWeight: "bold",
            shadowColor: "#E94057",
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.3,
            shadowRadius: 3,
          },
        }}
      />
      <Tabs.Screen
        name="chat"
        options={{
          tabBarIcon: ({ color, size, focused }) => (
            <View
              className={
                focused
                  ? "bg-[rgba(233,64,87,0.15)] p-3 rounded-full shadow-sm"
                  : "p-3 rounded-full"
              }
            >
              <Ionicons name="chatbubble" size={size} color={color} />
            </View>
          ),
          tabBarBadge: 5,
          tabBarBadgeStyle: {
            backgroundColor: "#E94057",
            fontSize: 10,
            fontWeight: "bold",
            shadowColor: "#E94057",
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.3,
            shadowRadius: 3,
          },
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          tabBarIcon: ({ color, size, focused }) => (
            <View
              className={
                focused
                  ? "bg-[rgba(233,64,87,0.15)] p-3 rounded-full shadow-sm"
                  : "p-3 rounded-full"
              }
            >
              <Ionicons name="person" size={30} color={color} />
            </View>
          ),
        }}
      />
    </Tabs>
  );
};

export default TabLayout;
