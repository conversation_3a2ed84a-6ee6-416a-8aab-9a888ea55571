import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { supabase } from "../../lib/supabase";

const RelationshipPreferences = () => {
  const router = useRouter();
  const { userId } = useLocalSearchParams();
  const [preferences, setPreferences] = useState<
    { id: number; name: string }[]
  >([]);
  const [selected, setSelected] = useState<number[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPreferences = async () => {
      const { data, error } = await supabase
        .from("relationship_preferences")
        .select("id, name");
      if (error) Alert.alert("Error", error.message);
      else setPreferences(data || []);
      setLoading(false);
    };
    fetchPreferences();
  }, []);

  const togglePreference = (id: number) => {
    setSelected((prev) =>
      prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]
    );
  };

  const handleNext = async () => {
    if (selected.length === 0) {
      Alert.alert("Select at least one preference");
      return;
    }
    setLoading(true);
    await supabase
      .from("user_relationship_preferences")
      .delete()
      .eq("user_id", userId);
    const inserts = selected.map((preference_id) => ({
      user_id: userId,
      preference_id,
    }));
    const { error } = await supabase
      .from("user_relationship_preferences")
      .insert(inserts);
    setLoading(false);
    if (error) Alert.alert("Error", error.message);
    else
      router.push({
        pathname: "./user-photos",
        params: { userId },
      });
  };

  if (loading) return <ActivityIndicator style={{ flex: 1 }} />;

  return (
    <View className="flex-1 p-6 pt-20 bg-white">
      <Text className="text-3xl font-bold mb-6">
        What are your relationship preferences?
      </Text>
      <FlatList
        data={preferences}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <TouchableOpacity
            onPress={() => togglePreference(item.id)}
            className={`p-4 mb-2 rounded-xl ${
              selected.includes(item.id) ? "bg-[#E94057]" : "bg-gray-100"
            }`}
          >
            <Text
              className={`text-lg ${
                selected.includes(item.id) ? "text-white" : "text-black"
              }`}
            >
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
      />
      <TouchableOpacity
        onPress={handleNext}
        className="mt-8 p-4 rounded-2xl bg-[#E94057]"
      >
        <Text className="text-white text-lg font-semibold text-center">
          Next
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default RelationshipPreferences;
