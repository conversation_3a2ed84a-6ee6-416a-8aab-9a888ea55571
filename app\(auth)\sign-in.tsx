import SignInCom from "@/components/auth/sign-in";
import { supabase } from "@/lib/supabase";
import { toast } from "@/lib/toast";
import { router } from "expo-router";
import React, { useState } from "react";
import { Platform, View } from "react-native";

const signin = () => {
  const [loading, setLoading] = useState(false);

  const handleSignIn = async (email: string) => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email: email,
        options: {
          shouldCreateUser: false, // Only for existing users
          emailRedirectTo:
            Platform.OS === "web" ? window.location.origin : undefined,
        },
      });

      if (error) {
        toast.error(error.message);
      } else {
        toast.success(
          "Check your email! We've sent a one-time password to your email address"
        );
        router.push({
          pathname: "/(auth)/verify-user",
          params: { email },
        });
      }
    } catch (error: any) {
      toast.error(error.message || "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = async (provider: "google") => {
    if (provider !== "google") return;

    setLoading(true);
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo:
            Platform.OS === "web"
              ? window.location.origin
              : "pyarwalaapp://auth",
        },
      });

      if (error) {
        toast.error(error.message);
      } else {
        toast.info("Redirecting to Google...");
      }
    } catch (error: any) {
      toast.error("Failed to sign in with Google");
    } finally {
      setLoading(false);
    }
  };

  return (
    <View className="flex-1">
      <SignInCom
        onSignIn={handleSignIn}
        onSocialLogin={handleSocialLogin}
        loading={loading}
      />
    </View>
  );
};

export default signin;
