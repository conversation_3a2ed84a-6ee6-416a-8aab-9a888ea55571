# Toast Message Usage Guide

This guide shows how to use the `react-native-toast-message` system that has been integrated into the Pyar Wala App.

## Setup

The toast system is already configured in the root layout (`app/_layout.tsx`) with custom styling that matches the app's theme colors.

## Basic Usage

Import the toast utility in any component:

```typescript
import { toast } from "@/lib/toast";
```

## Available Methods

### Success Messages
```typescript
toast.success("Profile updated successfully!");
toast.success("Match found!", { visibilityTime: 5000 });
```

### Error Messages
```typescript
toast.error("Failed to update profile");
toast.error("Network error occurred", { position: "bottom" });
```

### Info Messages
```typescript
toast.info("New message received");
toast.info("Redirecting to Google...", { visibilityTime: 2000 });
```

### Warning Messages
```typescript
toast.warning("You can select up to 10 interests");
toast.warning("Profile incomplete", { autoHide: false });
```

### Custom Messages
```typescript
toast.custom("custom", "Custom Title", "Custom message", {
  position: "bottom",
  visibilityTime: 4000
});
```

### Hide Toast
```typescript
toast.hide(); // Hides the currently displayed toast
```

## Configuration Options

All toast methods accept an optional `options` parameter:

```typescript
interface ToastOptions {
  text1?: string;        // Override title
  text2?: string;        // Override message
  position?: "top" | "bottom";
  visibilityTime?: number;  // Duration in milliseconds
  autoHide?: boolean;    // Whether to auto-hide
  topOffset?: number;    // Offset from top
  bottomOffset?: number; // Offset from bottom
}
```

## Examples

### Form Validation
```typescript
const handleSubmit = () => {
  if (!email) {
    toast.error("Please enter your email address");
    return;
  }
  
  if (!isValidEmail(email)) {
    toast.warning("Please enter a valid email address");
    return;
  }
  
  // Process form...
  toast.success("Form submitted successfully!");
};
```

### API Calls
```typescript
const updateProfile = async (data) => {
  try {
    const response = await api.updateProfile(data);
    toast.success("Profile updated successfully!");
    return response;
  } catch (error) {
    toast.error(error.message || "Failed to update profile");
    throw error;
  }
};
```

### Loading States
```typescript
const handleLogin = async () => {
  toast.info("Signing you in...");
  
  try {
    await signIn(credentials);
    toast.success("Welcome back!");
  } catch (error) {
    toast.error("Login failed. Please try again.");
  }
};
```

## Theme Colors

The toast messages are styled with the app's theme colors:

- **Success**: Green theme (`#10B981`, `#F0FDF4`)
- **Error**: Red theme (`#EF4444`, `#FEF2F2`)
- **Info**: Pink theme (`#EC4899`, `#FDF2F8`)
- **Warning**: Uses error styling for visibility

## Best Practices

1. **Keep messages concise** - Users should be able to read them quickly
2. **Use appropriate types** - Success for confirmations, error for failures, info for neutral updates
3. **Don't overuse** - Too many toasts can be annoying
4. **Consider timing** - Important messages should stay longer
5. **Test on different devices** - Ensure toasts are visible on various screen sizes

## Migration from Alert.alert

Replace existing `Alert.alert` calls:

```typescript
// Before
Alert.alert("Error", "Something went wrong");

// After
toast.error("Something went wrong");
```

```typescript
// Before
Alert.alert("Success", "Profile updated successfully");

// After
toast.success("Profile updated successfully");
```

## Integration Status

The toast system has been integrated into:

- ✅ Root Layout (`app/_layout.tsx`)
- ✅ Profile Setup (`app/(onboarding)/profile-setup.tsx`)
- ✅ Sign Up (`app/(auth)/sign-up.tsx`)
- 🔄 Other auth screens (in progress)
- 🔄 Main app screens (to be added)

## Next Steps

Continue replacing `Alert.alert` calls throughout the app with appropriate toast messages for a better user experience.
