import { supabase } from "./supabase";
import { toast } from "./toast";

/**
 * Database migration script to handle profile field updates
 * This script ensures all profiles have the required fields properly initialized
 */

export interface ProfileFieldUpdate {
  id: string;
  avatar_url?: string | null;
  about?: string | null;
  location_lat?: number | null;
  location_lng?: number | null;
  sexual_orientation?: string | null;
  looking_for?: string | null;
}

/**
 * Check and update profiles that have null values in required fields
 */
export const migrateProfileFields = async (): Promise<boolean> => {
  try {
    console.log("Starting profile fields migration...");

    // Fetch all profiles to check their current state
    const { data: profiles, error: fetchError } = await supabase
      .from("profiles")
      .select("*");

    if (fetchError) {
      console.error("Error fetching profiles for migration:", fetchError);
      return false;
    }

    if (!profiles || profiles.length === 0) {
      console.log("No profiles found to migrate");
      return true;
    }

    console.log(`Found ${profiles.length} profiles to check`);

    let updatedCount = 0;

    for (const profile of profiles) {
      const updates: Partial<ProfileFieldUpdate> = {};
      let needsUpdate = false;

      // Check each field and prepare updates if needed
      // Note: We're keeping these fields as null by design since they should be set by user input
      // This migration is mainly to ensure the database structure is consistent

      // Avatar URL - keep as null, will be set when user uploads photo
      if (profile.avatar_url === undefined) {
        updates.avatar_url = null;
        needsUpdate = true;
      }

      // About - keep as null, will be set when user adds bio
      if (profile.about === undefined) {
        updates.about = null;
        needsUpdate = true;
      }

      // Location coordinates - keep as null, will be set when user enables location
      if (profile.location_lat === undefined) {
        updates.location_lat = null;
        needsUpdate = true;
      }

      if (profile.location_lng === undefined) {
        updates.location_lng = null;
        needsUpdate = true;
      }

      // Sexual orientation - keep as null, will be set during onboarding
      if (profile.sexual_orientation === undefined) {
        updates.sexual_orientation = null;
        needsUpdate = true;
      }

      // Looking for - keep as null, will be set during onboarding
      if (profile.looking_for === undefined) {
        updates.looking_for = null;
        needsUpdate = true;
      }

      // Always update the timestamp
      updates.updated_at = new Date().toISOString();

      if (needsUpdate) {
        const { error: updateError } = await supabase
          .from("profiles")
          .update(updates)
          .eq("id", profile.id);

        if (updateError) {
          console.error(`Error updating profile ${profile.id}:`, updateError);
        } else {
          updatedCount++;
          console.log(`Updated profile ${profile.id}`);
        }
      }
    }

    console.log(`Migration completed. Updated ${updatedCount} profiles.`);
    return true;

  } catch (error) {
    console.error("Error in profile fields migration:", error);
    return false;
  }
};

/**
 * Initialize default values for new profiles
 * This ensures new profiles have all required fields properly set
 */
export const initializeNewProfile = async (userId: string, email: string): Promise<boolean> => {
  try {
    const profileData = {
      id: userId,
      username: email.split("@")[0],
      full_name: null,
      avatar_url: null,
      gender: null,
      age: null,
      about: null,
      is_first_time: true,
      location_lat: null,
      location_lng: null,
      notification_enabled: false,
      sexual_orientation: null,
      looking_for: null,
      last_active_at: new Date().toISOString(),
      profile_complete: false,
      visibility_status: "public",
      updated_at: new Date().toISOString(),
    };

    const { error } = await supabase
      .from("profiles")
      .insert(profileData);

    if (error) {
      console.error("Error creating new profile:", error);
      return false;
    }

    console.log(`Successfully created profile for user ${userId}`);
    return true;

  } catch (error) {
    console.error("Error in initializeNewProfile:", error);
    return false;
  }
};

/**
 * Update specific profile fields
 */
export const updateProfileField = async (
  userId: string, 
  field: keyof ProfileFieldUpdate, 
  value: any
): Promise<boolean> => {
  try {
    const updates = {
      [field]: value,
      updated_at: new Date().toISOString(),
    };

    const { error } = await supabase
      .from("profiles")
      .update(updates)
      .eq("id", userId);

    if (error) {
      console.error(`Error updating ${field} for user ${userId}:`, error);
      return false;
    }

    console.log(`Successfully updated ${field} for user ${userId}`);
    return true;

  } catch (error) {
    console.error(`Error in updateProfileField for ${field}:`, error);
    return false;
  }
};

/**
 * Batch update multiple profile fields
 */
export const updateMultipleProfileFields = async (
  userId: string,
  updates: Partial<ProfileFieldUpdate>
): Promise<boolean> => {
  try {
    const updateData = {
      ...updates,
      updated_at: new Date().toISOString(),
    };

    const { error } = await supabase
      .from("profiles")
      .update(updateData)
      .eq("id", userId);

    if (error) {
      console.error(`Error updating multiple fields for user ${userId}:`, error);
      return false;
    }

    console.log(`Successfully updated multiple fields for user ${userId}`);
    return true;

  } catch (error) {
    console.error("Error in updateMultipleProfileFields:", error);
    return false;
  }
};
