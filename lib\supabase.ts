import AsyncStorage from "@react-native-async-storage/async-storage";
import { createClient } from "@supabase/supabase-js";
import "react-native-url-polyfill/auto";

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || "";
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || "";

// Validate credentials to provide better error messages
if (!supabaseUrl || !supabaseAnonKey) {
  console.error(
    "Missing Supabase credentials. Please check your .env configuration."
  );
}

// Create a custom storage implementation that checks for window
const ExpoSecureStorage = {
  getItem: async (key: string) => {
    // Check if we're in a browser environment
    if (typeof window !== "undefined") {
      return AsyncStorage.getItem(key);
    }
    return null;
  },
  setItem: async (key: string, value: string) => {
    if (typeof window !== "undefined") {
      await AsyncStorage.setItem(key, value);
    }
    return;
  },
  removeItem: async (key: string) => {
    if (typeof window !== "undefined") {
      await AsyncStorage.removeItem(key);
    }
    return;
  },
};

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: ExpoSecureStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database utility functions
export const updateProfileFields = async () => {
  try {
    // This function can be used to update existing profiles with default values
    // for fields that are currently null
    const { data: profiles, error: fetchError } = await supabase
      .from("profiles")
      .select(
        "id, avatar_url, about, location_lat, location_lng, sexual_orientation, looking_for"
      )
      .or(
        "avatar_url.is.null,about.is.null,location_lat.is.null,location_lng.is.null,sexual_orientation.is.null,looking_for.is.null"
      );

    if (fetchError) {
      console.error("Error fetching profiles:", fetchError);
      return false;
    }

    if (profiles && profiles.length > 0) {
      console.log(`Found ${profiles.length} profiles with null fields`);

      // Update each profile with default values where needed
      for (const profile of profiles) {
        const updates: any = {};

        // Keep avatar_url as null - will be set when user uploads photo
        // Keep about as null - will be set when user adds bio
        // Keep location_lat/lng as null - will be set when user enables location
        // Keep sexual_orientation as null - will be set during onboarding
        // Keep looking_for as null - will be set during onboarding

        // Only update updated_at timestamp
        updates.updated_at = new Date().toISOString();

        const { error: updateError } = await supabase
          .from("profiles")
          .update(updates)
          .eq("id", profile.id);

        if (updateError) {
          console.error(`Error updating profile ${profile.id}:`, updateError);
        }
      }
    }

    return true;
  } catch (error) {
    console.error("Error in updateProfileFields:", error);
    return false;
  }
};
