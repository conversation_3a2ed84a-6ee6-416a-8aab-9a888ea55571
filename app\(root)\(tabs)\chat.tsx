import { Ionicons } from "@expo/vector-icons";
import React from "react";
import {
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const Chat = () => {
  return (
    <SafeAreaView className="flex-1 bg-pink-50">
      <StatusBar barStyle="dark-content" backgroundColor="#fdf2f8" />

      {/* Header */}
      <View className="px-6 py-4 bg-white border-b border-gray-100">
        <Text className="text-2xl font-bold text-gray-800">Messages</Text>
      </View>

      {/* Empty State */}
      <View className="flex-1 justify-center items-center p-6">
        <Ionicons name="chatbubbles-outline" size={80} color="#FF6B8A" />
        <Text className="text-2xl font-bold text-gray-800 mt-4 text-center">
          No conversations yet
        </Text>
        <Text className="text-gray-600 mt-2 text-center">
          Start matching with people to begin chatting!
        </Text>

        <TouchableOpacity className="mt-6 bg-pink-500 px-6 py-3 rounded-full">
          <Text className="text-white font-semibold">Start Discovering</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default Chat;
