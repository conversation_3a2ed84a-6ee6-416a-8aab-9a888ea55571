import { useAuthStore } from "@/stores/authStore";
import { useProfileStore } from "@/stores/profileStore";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import PhotoGallery from "../../components/photo-gallery";

const UserPhotos = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const { photos, fetchProfile } = useProfileStore();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user?.id) {
      fetchProfile(user.id);
    }
  }, [user]);

  const handleContinue = async () => {
    try {
      setLoading(true);

      // Check if user has at least one photo
      if (photos.length === 0) {
        Alert.alert(
          "No Photos",
          "Please upload at least one photo to continue.",
          [{ text: "OK" }]
        );
        return;
      }

      // Navigate to main app since profile setup is complete
      router.replace("/(root)/(tabs)/main");
    } catch (error) {
      if (error instanceof Error) {
        Alert.alert("Error", error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center">
        <Text>Please login to continue</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1" style={{ backgroundColor: "#FF9A8B20" }}>
      <StatusBar barStyle="dark-content" backgroundColor="#FF9A8B20" />

      <ScrollView className="flex-1">
        <View className="p-6">
          <Text className="text-4xl font-bold text-black mb-4">
            Your Photos
          </Text>
          <Text className="text-gray-600 mb-8">
            Upload your best photos to make your profile stand out. The first
            photo will be your profile picture.
          </Text>

          <View className="bg-white rounded-3xl p-6 mb-6 shadow-sm">
            <PhotoGallery userId={user.id} maxPhotos={6} />
          </View>
        </View>
      </ScrollView>

      <View className="p-6 bg-white border-t border-gray-200">
        <View className="flex-row justify-between">
          <TouchableOpacity
            onPress={() => router.back()}
            className="flex-1 p-4 mr-2 rounded-2xl bg-white border border-red-400"
          >
            <Text className="text-red-400 text-lg font-semibold text-center">
              Back
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleContinue}
            disabled={loading}
            className="flex-1 p-4 ml-2 rounded-2xl bg-[#E94057]"
          >
            <Text className="text-white text-lg font-semibold text-center">
              {loading ? "Processing..." : "Continue"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default UserPhotos;
