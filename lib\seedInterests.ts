import { supabase } from "./supabase";

const interestsList = [
  "Music",
  "Movies",
  "Reading",
  "Travel",
  "Cooking",
  "Sports",
  "Gaming",
  "Art",
  "Photography",
  "Dancing",
  "Fitness",
  "Hiking",
  "Swimming",
  "Cycling",
  "Running",
  "Yoga",
  "Meditation",
  "Writing",
  "Fashion",
  "Technology",
  "Science",
  "Politics",
  "History",
  "Philosophy",
  "Animals",
  "Nature",
  "Gardening",
  "DIY",
  "Crafts",
  "Baking",
  "Foodie",
  "Wine",
  "Beer",
  "Coffee",
  "Tea",
  "Volunteering",
  "Activism",
  "Singing",
  "Playing Instruments",
  "Concerts",
  "Festivals",
  "Theater",
  "Standup Comedy",
  "Podcasts",
  "Languages",
];

const relationshipPreferencesList = [
  "Long-term relationship",
  "Short-term relationship",
  "Casual dating",
  "Marriage",
  "Friendship",
  "Hookups",
  "Something casual",
  "Don't know yet",
  "New friends",
  "Life partner",
  "Travel companion",
  "Activity partner",
  "Pen pal",
  "Networking",
  "Mentorship",
  "Creative collaboration",
];

export const seedInterests = async () => {
  try {
    const { data: existingInterests, error: fetchError } = await supabase
      .from("interests")
      .select("name");

    if (fetchError) throw fetchError;

    const existingNames = existingInterests?.map((i) => i.name) || [];
    const newInterests = interestsList.filter(
      (name) => !existingNames.includes(name)
    );

    if (newInterests.length === 0) {
      console.log("All interests already exist in the database");
      return;
    }

    const interestsToInsert = newInterests.map((name) => ({ name }));

    const { error } = await supabase
      .from("interests")
      .insert(interestsToInsert);

    if (error) throw error;

    console.log(`Successfully seeded ${newInterests.length} new interests`);
  } catch (error: any) {
    console.error("Error seeding interests:", error.message);
  }
};

export const seedRelationshipPreferences = async () => {
  try {
    const { data: existingPrefs, error: fetchError } = await supabase
      .from("relationship_preferences")
      .select("name");

    if (fetchError) throw fetchError;

    const existingNames = existingPrefs?.map((p) => p.name) || [];
    const newPreferences = relationshipPreferencesList.filter(
      (name) => !existingNames.includes(name)
    );

    if (newPreferences.length === 0) {
      console.log("All relationship preferences already exist in the database");
      return;
    }

    const preferencesToInsert = newPreferences.map((name) => ({ name }));

    const { error } = await supabase
      .from("relationship_preferences")
      .insert(preferencesToInsert);

    if (error) throw error;

    console.log(
      `Successfully seeded ${newPreferences.length} new relationship preferences`
    );
  } catch (error: any) {
    console.error("Error seeding relationship preferences:", error.message);
  }
};

export const seedAllData = async () => {
  console.log("Starting data seeding...");
  await seedInterests();
  await seedRelationshipPreferences();
  console.log("Data seeding completed!");
};
