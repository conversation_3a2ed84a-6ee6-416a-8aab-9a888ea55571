/**
 * <PERSON><PERSON><PERSON> to update existing profiles with the new database structure
 * Run this script once to ensure all existing profiles have the required fields
 * 
 * Usage: 
 * 1. Import this in your app and call updateExistingProfiles()
 * 2. Or run it as a standalone script if you have a Node.js environment
 */

import { supabase } from "../lib/supabase";

interface ProfileUpdate {
  id: string;
  avatar_url?: string | null;
  about?: string | null;
  location_lat?: number | null;
  location_lng?: number | null;
  sexual_orientation?: string | null;
  looking_for?: string | null;
  updated_at: string;
}

/**
 * Update all existing profiles to ensure they have the required fields
 */
export const updateExistingProfiles = async (): Promise<boolean> => {
  try {
    console.log("Starting profile update process...");

    // Fetch all existing profiles
    const { data: profiles, error: fetchError } = await supabase
      .from("profiles")
      .select("*");

    if (fetchError) {
      console.error("Error fetching profiles:", fetchError);
      return false;
    }

    if (!profiles || profiles.length === 0) {
      console.log("No profiles found to update");
      return true;
    }

    console.log(`Found ${profiles.length} profiles to check and update`);

    let updatedCount = 0;
    let errorCount = 0;

    for (const profile of profiles) {
      try {
        const updates: ProfileUpdate = {
          id: profile.id,
          updated_at: new Date().toISOString(),
        };

        let needsUpdate = false;

        // Check and set avatar_url to null if undefined
        if (profile.avatar_url === undefined) {
          updates.avatar_url = null;
          needsUpdate = true;
        }

        // Check and set about to null if undefined
        if (profile.about === undefined) {
          updates.about = null;
          needsUpdate = true;
        }

        // Check and set location_lat to null if undefined
        if (profile.location_lat === undefined) {
          updates.location_lat = null;
          needsUpdate = true;
        }

        // Check and set location_lng to null if undefined
        if (profile.location_lng === undefined) {
          updates.location_lng = null;
          needsUpdate = true;
        }

        // Check and set sexual_orientation to null if undefined
        if (profile.sexual_orientation === undefined) {
          updates.sexual_orientation = null;
          needsUpdate = true;
        }

        // Check and set looking_for to null if undefined
        if (profile.looking_for === undefined) {
          updates.looking_for = null;
          needsUpdate = true;
        }

        if (needsUpdate) {
          const { error: updateError } = await supabase
            .from("profiles")
            .update(updates)
            .eq("id", profile.id);

          if (updateError) {
            console.error(`Error updating profile ${profile.id}:`, updateError);
            errorCount++;
          } else {
            console.log(`Successfully updated profile ${profile.id}`);
            updatedCount++;
          }
        } else {
          console.log(`Profile ${profile.id} already up to date`);
        }

        // Add a small delay to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        console.error(`Error processing profile ${profile.id}:`, error);
        errorCount++;
      }
    }

    console.log(`Profile update completed:`);
    console.log(`- Updated: ${updatedCount} profiles`);
    console.log(`- Errors: ${errorCount} profiles`);
    console.log(`- Total processed: ${profiles.length} profiles`);

    return errorCount === 0;

  } catch (error) {
    console.error("Error in updateExistingProfiles:", error);
    return false;
  }
};

/**
 * Verify that all profiles have the required fields
 */
export const verifyProfileStructure = async (): Promise<boolean> => {
  try {
    console.log("Verifying profile structure...");

    const { data: profiles, error } = await supabase
      .from("profiles")
      .select("id, avatar_url, about, location_lat, location_lng, sexual_orientation, looking_for");

    if (error) {
      console.error("Error fetching profiles for verification:", error);
      return false;
    }

    if (!profiles || profiles.length === 0) {
      console.log("No profiles found");
      return true;
    }

    let issuesFound = 0;

    for (const profile of profiles) {
      const issues: string[] = [];

      if (profile.avatar_url === undefined) issues.push("avatar_url");
      if (profile.about === undefined) issues.push("about");
      if (profile.location_lat === undefined) issues.push("location_lat");
      if (profile.location_lng === undefined) issues.push("location_lng");
      if (profile.sexual_orientation === undefined) issues.push("sexual_orientation");
      if (profile.looking_for === undefined) issues.push("looking_for");

      if (issues.length > 0) {
        console.log(`Profile ${profile.id} has undefined fields: ${issues.join(", ")}`);
        issuesFound++;
      }
    }

    if (issuesFound === 0) {
      console.log("All profiles have the required field structure ✓");
      return true;
    } else {
      console.log(`Found ${issuesFound} profiles with structural issues`);
      return false;
    }

  } catch (error) {
    console.error("Error verifying profile structure:", error);
    return false;
  }
};

/**
 * Main function to run the complete profile update process
 */
export const runCompleteProfileUpdate = async (): Promise<void> => {
  try {
    console.log("=== Starting Complete Profile Update Process ===");

    // First, verify current structure
    console.log("\n1. Verifying current profile structure...");
    const structureOk = await verifyProfileStructure();

    if (!structureOk) {
      console.log("\n2. Updating profiles with missing fields...");
      const updateSuccess = await updateExistingProfiles();

      if (updateSuccess) {
        console.log("\n3. Re-verifying profile structure...");
        await verifyProfileStructure();
      } else {
        console.error("Profile update failed");
      }
    } else {
      console.log("Profile structure is already correct, no updates needed");
    }

    console.log("\n=== Profile Update Process Complete ===");

  } catch (error) {
    console.error("Error in runCompleteProfileUpdate:", error);
  }
};

// Export for use in the app
export default {
  updateExistingProfiles,
  verifyProfileStructure,
  runCompleteProfileUpdate,
};
