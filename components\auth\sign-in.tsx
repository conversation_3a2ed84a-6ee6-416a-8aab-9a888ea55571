import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

interface SignInScreenProps {
  onSignIn: (email: string) => void;
  onSocialLogin: (provider: "google") => void;
  loading?: boolean;
}

interface FormData {
  email: string;
}

interface FormError {
  email?: string;
  general?: string;
}

const SignInCom: React.FC<SignInScreenProps> = ({
  onSignIn,
  onSocialLogin,
  loading = false,
}) => {
  const [formData, setFormData] = useState<FormData>({
    email: "",
  });
  const [errors, setErrors] = useState<FormError>({});

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormError = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignIn = () => {
    if (validateForm()) {
      onSignIn(formData.email);
    }
  };

  const renderInputField = (
    label: string,
    field: keyof FormData,
    placeholder: string
  ) => {
    return (
      <View className="mb-5">
        <Text className="text-gray-700 font-medium mb-2 ml-1">{label}</Text>
        <View
          className={`bg-white rounded-xl border ${
            errors[field] ? "border-red-500" : "border-gray-200"
          } px-4 py-3 shadow-sm flex-row items-center`}
        >
          <TextInput
            value={formData[field]}
            onChangeText={(value) => handleInputChange(field, value)}
            placeholder={placeholder}
            autoCapitalize="none"
            autoComplete="email"
            className="flex-1 text-base text-gray-900"
            placeholderTextColor="#9CA3AF"
          />
        </View>
        {errors[field] && (
          <Text className="text-red-500 text-xs ml-1 mt-1">
            {errors[field]}
          </Text>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-white pt-10">
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        className="flex-1"
      >
        <ScrollView
          className="flex-1 px-5"
          contentContainerStyle={{ paddingTop: 32, paddingBottom: 24 }}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View className="flex-1 mb-8">
            <Text className="text-3xl font-bold text-gray-900 mb-2">
              Sign In To Your Account
            </Text>
            <Text className="text-gray-500 text-base">
              We'll send you a one-time password to your email
            </Text>
          </View>

          {/* Form Fields */}
          <View className="mb-2">
            {renderInputField("Email Address", "email", "Enter your email")}
          </View>

          {/* Sign In Button */}
          <TouchableOpacity
            className="mb-8 rounded-full bg-[#E94057] p-4"
            onPress={handleSignIn}
            activeOpacity={0.8}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#ffffff" />
            ) : (
              <Text className="text-white text-lg font-semibold text-center">
                Send OTP
              </Text>
            )}
          </TouchableOpacity>

          {/* Divider */}
          <View className="flex-row items-center mb-8">
            <View className="flex-1 h-px bg-gray-200" />
            <Text className="mx-4 text-gray-400 text-sm">or sign in with</Text>
            <View className="flex-1 h-px bg-gray-200" />
          </View>

          {/* Google Login Button */}
          <TouchableOpacity
            className="flex-row items-center justify-center bg-white border border-gray-200 rounded-xl py-3 px-4 shadow-sm mb-8"
            onPress={() => onSocialLogin("google")}
            activeOpacity={0.7}
            disabled={loading}
          >
            <Ionicons name="logo-google" size={22} color="#EA4335" />
            <Text className="ml-2 font-medium text-gray-700">
              Continue with Google
            </Text>
          </TouchableOpacity>

          {/* Sign Up Link */}
          <View className="flex-row justify-center">
            <Text className="text-gray-600">Don't have an account? </Text>
            <TouchableOpacity onPress={() => router.push("/(auth)/sign-up")}>
              <Text className="text-pink-500 font-medium">Sign Up</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default SignInCom;
