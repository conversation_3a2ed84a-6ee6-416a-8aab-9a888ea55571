import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { Text, TouchableOpacity, View } from "react-native";

interface PhotoGalleryProps {
  userId: string;
  onProfilePhotoChange?: (url: string) => void;
  maxPhotos?: number;
}

export default function PhotoGallery({
  userId,
  onProfilePhotoChange,
  maxPhotos = 6,
}: PhotoGalleryProps) {
  return (
    <View className="flex-1 p-4">
      <View className="mb-4">
        <Text className="text-lg font-bold text-gray-800">My Photos</Text>
        <Text className="text-gray-600 text-sm">
          0/{maxPhotos} photos uploaded
        </Text>
      </View>

      <View className="flex-row flex-wrap justify-between">
        <TouchableOpacity className="w-[48%] aspect-[3/4] bg-gray-100 rounded-xl border border-dashed border-gray-300 justify-center items-center mb-4">
          <View className="w-16 h-16 bg-pink-500 rounded-full justify-center items-center">
            <Ionicons name="add" size={24} color="white" />
          </View>
          <Text className="mt-2 text-gray-600 font-semibold">Add Photo</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
