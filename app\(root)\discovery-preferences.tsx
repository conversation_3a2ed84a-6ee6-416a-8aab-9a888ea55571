import { supabase } from "@/lib/supabase";
import { Ionicons } from "@expo/vector-icons";
import Slider from "@react-native-community/slider";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface DiscoveryPreference {
  id: number;
  user_id: string;
  min_age: number;
  max_age: number;
  max_distance: number;
  preferred_genders: string[];
  show_me: string;
  deal_breakers: string[];
  must_haves: string[];
  updated_at: string;
}

const DiscoveryPreferencesScreen = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [preferences, setPreferences] = useState<DiscoveryPreference | null>(
    null
  );

  // UI state
  const [minAge, setMinAge] = useState(18);
  const [maxAge, setMaxAge] = useState(25);
  const [maxDistance, setMaxDistance] = useState(50);
  const [preferredGenders, setPreferredGenders] = useState<string[]>([]);
  const [showMe, setShowMe] = useState("all");
  const [dealBreakers, setDealBreakers] = useState<string[]>([]);
  const [mustHaves, setMustHaves] = useState<string[]>([]);

  // Available options for deal breakers and must haves
  const dealBreakerOptions = [
    "smoking",
    "drinking",
    "has_children",
    "wants_children",
    "religious",
    "politics",
    "casual_dating",
  ];

  const mustHaveOptions = [
    "same_religion",
    "same_politics",
    "wants_marriage",
    "wants_children",
    "has_job",
    "has_education",
    "active_lifestyle",
  ];

  useEffect(() => {
    fetchUserPreferences();
  }, []);

  const fetchUserPreferences = async () => {
    setIsLoading(true);
    try {
      const { data: session } = await supabase.auth.getSession();

      if (!session.session?.user) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from("discovery_preferences")
        .select("*")
        .eq("user_id", session.session.user.id)
        .single();

      if (error) {
        if (error.code !== "PGRST116") {
          // No rows returned
          console.error("Error fetching preferences:", error);
        }
        // If no preferences found, we'll use the defaults already set in state
      } else if (data) {
        // Update state with fetched preferences
        setMinAge(data.min_age);
        setMaxAge(data.max_age);
        setMaxDistance(data.max_distance);
        setPreferredGenders(data.preferred_genders || []);
        setShowMe(data.show_me);
        setDealBreakers(data.deal_breakers || []);
        setMustHaves(data.must_haves || []);
        setPreferences(data);
      }
    } catch (error: any) {
      console.error("Error in fetchUserPreferences:", error.message);
      Alert.alert("Error", "Failed to load discovery preferences");
    } finally {
      setIsLoading(false);
    }
  };

  const savePreferences = async () => {
    setIsLoading(true);
    try {
      const { data: session } = await supabase.auth.getSession();

      if (!session.session?.user) {
        throw new Error("User not authenticated");
      }

      const userId = session.session.user.id;

      const updatedPreferences = {
        user_id: userId,
        min_age: minAge,
        max_age: maxAge,
        max_distance: maxDistance,
        preferred_genders: preferredGenders,
        show_me: showMe,
        deal_breakers: dealBreakers,
        must_haves: mustHaves,
      };

      // Use upsert to handle both insert and update cases
      const { error } = await supabase
        .from("discovery_preferences")
        .upsert(updatedPreferences, { onConflict: "user_id" });

      if (error) {
        throw error;
      }

      Alert.alert("Success", "Discovery preferences updated successfully");
      router.back();
    } catch (error: any) {
      console.error("Error saving preferences:", error.message);
      Alert.alert("Error", "Failed to save discovery preferences");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleGenderPreference = (gender: string) => {
    setPreferredGenders((prev) => {
      if (prev.includes(gender)) {
        return prev.filter((g) => g !== gender);
      } else {
        return [...prev, gender];
      }
    });
  };

  const toggleDealBreaker = (option: string) => {
    setDealBreakers((prev) => {
      if (prev.includes(option)) {
        return prev.filter((item) => item !== option);
      } else {
        return [...prev, option];
      }
    });
  };

  const toggleMustHave = (option: string) => {
    setMustHaves((prev) => {
      if (prev.includes(option)) {
        return prev.filter((item) => item !== option);
      } else {
        return [...prev, option];
      }
    });
  };

  return (
    <SafeAreaView className="flex-1">
      <StatusBar barStyle="dark-content" backgroundColor="#FF9A8B20" />
      <View className="flex-row items-center p-4 bg-white border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()} className="mr-4">
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold">Discovery Preferences</Text>
      </View>

      <ScrollView
        className="flex-1 p-6"
        style={{ backgroundColor: "#FF9A8B20" }}
      >
        <Text className="text-base text-gray-600 mb-6">
          Customize who you see and how you're discovered on PyarWala
        </Text>

        {/* Age Range Preferences */}
        <View className="bg-white rounded-3xl p-6 mb-6 shadow-sm">
          <Text className="text-lg font-semibold mb-4">Age Range</Text>

          <View className="mb-6">
            <View className="flex-row justify-between mb-2">
              <Text className="text-gray-600">Minimum Age:</Text>
              <Text className="font-medium">{minAge} years</Text>
            </View>
            <Slider
              minimumValue={18}
              maximumValue={70}
              step={1}
              value={minAge}
              onValueChange={setMinAge}
              minimumTrackTintColor="#E94057"
              maximumTrackTintColor="#d3d3d3"
              thumbTintColor="#E94057"
              className="mb-4"
            />
          </View>

          <View>
            <View className="flex-row justify-between mb-2">
              <Text className="text-gray-600">Maximum Age:</Text>
              <Text className="font-medium">{maxAge} years</Text>
            </View>
            <Slider
              minimumValue={18}
              maximumValue={70}
              step={1}
              value={maxAge}
              onValueChange={(val) => setMaxAge(Math.max(val, minAge))}
              minimumTrackTintColor="#E94057"
              maximumTrackTintColor="#d3d3d3"
              thumbTintColor="#E94057"
            />
          </View>
        </View>

        {/* Distance Preference */}
        <View className="bg-white rounded-3xl p-6 mb-6 shadow-sm">
          <Text className="text-lg font-semibold mb-4">Maximum Distance</Text>
          <View className="flex-row justify-between mb-2">
            <Text className="text-gray-600">Show people within:</Text>
            <Text className="font-medium">{maxDistance} km</Text>
          </View>
          <Slider
            minimumValue={1}
            maximumValue={100}
            step={1}
            value={maxDistance}
            onValueChange={setMaxDistance}
            minimumTrackTintColor="#E94057"
            maximumTrackTintColor="#d3d3d3"
            thumbTintColor="#E94057"
          />
        </View>

        {/* Gender Preferences */}
        <View className="bg-white rounded-3xl p-6 mb-6 shadow-sm">
          <Text className="text-lg font-semibold mb-4">Show Me</Text>

          <View className="flex-row justify-between mb-6">
            <TouchableOpacity
              onPress={() => setShowMe("all")}
              className={`flex-1 p-4 rounded-2xl mr-2 ${
                showMe === "all"
                  ? "bg-[#E94057]"
                  : "bg-white border border-gray-200"
              }`}
            >
              <Text
                className={`text-center font-medium ${
                  showMe === "all" ? "text-white" : "text-black"
                }`}
              >
                Everyone
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setShowMe("preferences")}
              className={`flex-1 p-4 rounded-2xl ml-2 ${
                showMe === "preferences"
                  ? "bg-[#E94057]"
                  : "bg-white border border-gray-200"
              }`}
            >
              <Text
                className={`text-center font-medium ${
                  showMe === "preferences" ? "text-white" : "text-black"
                }`}
              >
                My Preferences
              </Text>
            </TouchableOpacity>
          </View>

          {showMe === "preferences" && (
            <View className="flex flex-col gap-3">
              <Text className="text-base font-medium mb-2">
                Gender Preferences
              </Text>

              <TouchableOpacity
                onPress={() => toggleGenderPreference("woman")}
                className={`p-4 rounded-2xl flex-row justify-between items-center ${
                  preferredGenders.includes("woman")
                    ? "bg-[#E94057]"
                    : "bg-white border border-gray-200"
                }`}
              >
                <Text
                  className={`font-medium ${
                    preferredGenders.includes("woman")
                      ? "text-white"
                      : "text-black"
                  }`}
                >
                  Women
                </Text>
                {preferredGenders.includes("woman") && (
                  <Text className="text-white text-xl">✓</Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => toggleGenderPreference("man")}
                className={`p-4 rounded-2xl flex-row justify-between items-center ${
                  preferredGenders.includes("man")
                    ? "bg-[#E94057]"
                    : "bg-white border border-gray-200"
                }`}
              >
                <Text
                  className={`font-medium ${
                    preferredGenders.includes("man")
                      ? "text-white"
                      : "text-black"
                  }`}
                >
                  Men
                </Text>
                {preferredGenders.includes("man") && (
                  <Text className="text-white text-xl">✓</Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => toggleGenderPreference("other")}
                className={`p-4 rounded-2xl flex-row justify-between items-center ${
                  preferredGenders.includes("other")
                    ? "bg-[#E94057]"
                    : "bg-white border border-gray-200"
                }`}
              >
                <Text
                  className={`font-medium ${
                    preferredGenders.includes("other")
                      ? "text-white"
                      : "text-black"
                  }`}
                >
                  Non-binary/Other
                </Text>
                {preferredGenders.includes("other") && (
                  <Text className="text-white text-xl">✓</Text>
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Deal Breakers */}
        <View className="bg-white rounded-3xl p-6 mb-6 shadow-sm">
          <Text className="text-lg font-semibold mb-2">Deal Breakers</Text>
          <Text className="text-gray-600 mb-4">
            People with these traits won't appear in your matches
          </Text>

          <View className="flex-row flex-wrap">
            {dealBreakerOptions.map((option) => (
              <TouchableOpacity
                key={option}
                onPress={() => toggleDealBreaker(option)}
                className={`m-1 px-4 py-2 rounded-full ${
                  dealBreakers.includes(option)
                    ? "bg-[#E94057]"
                    : "bg-white border border-gray-300"
                }`}
              >
                <Text
                  className={
                    dealBreakers.includes(option) ? "text-white" : "text-black"
                  }
                >
                  {option
                    .split("_")
                    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(" ")}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Must Haves */}
        <View className="bg-white rounded-3xl p-6 mb-8 shadow-sm">
          <Text className="text-lg font-semibold mb-2">Must Haves</Text>
          <Text className="text-gray-600 mb-4">
            Prioritize matches with these traits
          </Text>

          <View className="flex-row flex-wrap">
            {mustHaveOptions.map((option) => (
              <TouchableOpacity
                key={option}
                onPress={() => toggleMustHave(option)}
                className={`m-1 px-4 py-2 rounded-full ${
                  mustHaves.includes(option)
                    ? "bg-[#E94057]"
                    : "bg-white border border-gray-300"
                }`}
              >
                <Text
                  className={
                    mustHaves.includes(option) ? "text-white" : "text-black"
                  }
                >
                  {option
                    .split("_")
                    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(" ")}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Save Button */}
        <TouchableOpacity
          onPress={savePreferences}
          disabled={isLoading}
          className={`p-4 rounded-2xl mb-8 ${
            isLoading ? "bg-gray-300" : "bg-[#E94057]"
          }`}
        >
          <Text className="text-white text-lg font-semibold text-center">
            {isLoading ? "Saving..." : "Save Preferences"}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

export default DiscoveryPreferencesScreen;
