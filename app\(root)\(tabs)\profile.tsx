import { useAuthStore } from "@/stores/authStore";
import { useProfileStore } from "@/stores/profileStore";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useEffect, useState } from "react";
import {
  Alert,
  Image,
  Modal,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import Avatar from "../../../components/avatar";
import PhotoGallery from "../../../components/photo-gallery";

const Profile = () => {
  const router = useRouter();
  const { user, signOut } = useAuthStore();
  const {
    profile,
    photos,
    userInterests,
    fetchProfile,
    updateProfile,
    loading: profileLoading,
  } = useProfileStore();
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    username: "",
    full_name: "",
    about: "",
  });
  const [signingOut, setSigningOut] = useState(false);

  // Dummy data for non-editable fields
  const dummyData = {
    location: "Chicago, IL United States",
    distance: "1 km",
    profession: "Professional model",
    about:
      "My name is <PERSON> and I enjoy meeting new people and finding ways to help them have an uplifting experience. I enjoy reading..",
    interests: ["Travelling", "Books", "Music", "Dancing", "Modeling"],
    gallery: [
      "https://images.unsplash.com/photo-1494790108755-2616b612b77c?w=400",
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400",
      "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400",
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400",
    ],
  };

  useEffect(() => {
    if (user?.id) {
      fetchProfile(user.id);
    }
  }, [user]);

  useEffect(() => {
    if (profile) {
      setEditData({
        username: profile.username || "",
        full_name: profile.full_name || "",
        about: profile.about || "",
      });
    }
  }, [profile]);

  const handleUpdateProfile = async () => {
    if (!user?.id) return;

    const success = await updateProfile({
      ...editData,
      updated_at: new Date().toISOString(),
    });

    if (success) {
      setIsEditing(false);
      Alert.alert("Success", "Profile updated successfully!");
    } else {
      Alert.alert("Error", "Failed to update profile");
    }
  };

  const handleSignOut = async () => {
    setSigningOut(true);
    try {
      await signOut();
      Alert.alert("Signed out", "You have been signed out.");
    } catch (error: any) {
      Alert.alert("Error", error?.message || "Failed to sign out.");
    } finally {
      setSigningOut(false);
    }
  };

  const displayName = profile?.full_name || profile?.username || "User";
  const displayAge = profile?.age || 23;

  return (
    <ScrollView
      className="flex-1 bg-gray-50"
      contentContainerStyle={{ paddingTop: 32 }}
    >
      {/* Header */}
      <View className="flex-row items-center justify-between p-4 pt-12 bg-white">
        <View className="w-10 h-10" />
        <Text className="text-lg font-semibold">Profile</Text>
        <TouchableOpacity
          onPress={() => setIsEditing(true)}
          className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
        >
          <Ionicons name="pencil" size={18} color="#666" />
        </TouchableOpacity>
      </View>

      {/* Main Profile Card */}
      <View className="mx-4 mt-8 bg-white rounded-3xl overflow-hidden shadow-sm border border-gray-100">
        {/* Profile Image */}
        <View className="w-full h-56 bg-gray-200 justify-center items-center">
          <Image
            source={{
              uri:
                profile?.avatar_url ||
                "https://images.unsplash.com/photo-1494790108755-2616b612b77c?w=600",
            }}
            className="w-full h-full"
            resizeMode="cover"
            style={{ borderRadius: 0 }}
          />
        </View>

        {/* Profile Info */}
        <View className="p-6">
          {/* Name and Age */}
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-2xl font-bold text-gray-900">
              {displayName}, {displayAge}
            </Text>
          </View>

          {profile?.about && (
            <Text className="text-gray-600 mb-4">{profile.about}</Text>
          )}

          {/* About */}
          {profile?.about && (
            <View className="mb-6">
              <Text className="text-lg font-semibold text-gray-900 mb-2">
                About
              </Text>
              <Text className="text-gray-700 leading-6">{profile.about}</Text>
            </View>
          )}

          {/* Interests */}
          {userInterests.length > 0 && (
            <View className="mb-6">
              <Text className="text-lg font-semibold text-gray-900 mb-3">
                Interests
              </Text>
              <View className="flex-row flex-wrap">
                {userInterests.map((userInterest, index) => (
                  <View
                    key={userInterest.id}
                    className="mr-2 mb-2 px-4 py-2 rounded-full bg-pink-50 border border-pink-200"
                  >
                    <Text className="text-pink-600 font-medium">
                      {userInterest.interest?.name}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Gallery */}
          <View className="mb-6">
            <View className="flex-row items-center justify-between mb-3">
              <Text className="text-lg font-semibold text-gray-900">
                Gallery
              </Text>
              <TouchableOpacity>
                <Text className="text-red-500 font-medium">See all</Text>
              </TouchableOpacity>
            </View>

            {user?.id && <PhotoGallery userId={user.id} />}
          </View>

          {/* Discovery Preferences Button */}
          <TouchableOpacity
            onPress={() => router.push("./discovery-preferences")}
            className="flex-row items-center px-4 py-3 bg-gray-50 rounded-xl mb-4"
          >
            <Ionicons name="options-outline" size={24} color="#E94057" />
            <Text className="ml-3 text-lg text-gray-800">
              Discovery Preferences
            </Text>
            <Ionicons
              name="chevron-forward"
              size={24}
              color="#999"
              style={{ marginLeft: "auto" }}
            />
          </TouchableOpacity>

          {/* Sign Out Button */}
          <TouchableOpacity
            onPress={handleSignOut}
            className={`bg-gray-100 p-4 rounded-xl mt-4 ${
              signingOut ? "opacity-50" : ""
            }`}
            disabled={signingOut}
          >
            <Text className="text-gray-700 font-semibold text-center">
              {signingOut ? "Signing Out..." : "Sign Out"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Edit Profile Modal */}
      <Modal
        visible={isEditing}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View className="flex-1 bg-white">
          {/* Modal Header */}
          <View className="flex-row items-center justify-between p-4 pt-12 border-b border-gray-200">
            <TouchableOpacity onPress={() => setIsEditing(false)}>
              <Text className="text-red-500 font-medium">Cancel</Text>
            </TouchableOpacity>
            <Text className="text-lg font-semibold">Edit Profile</Text>
            <TouchableOpacity
              onPress={handleUpdateProfile}
              disabled={profileLoading}
            >
              <Text
                className={`font-medium ${
                  profileLoading ? "text-gray-400" : "text-red-500"
                }`}
              >
                {profileLoading ? "Saving..." : "Save"}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView className="flex-1 p-4">
            {/* Avatar Upload */}
            <View className="items-center mb-6">
              <Avatar
                size={120}
                url={profile?.avatar_url || ""}
                onUpload={(url: string) => {
                  updateProfile({ avatar_url: url });
                }}
              />
              <Text className="text-gray-600 mt-2">Tap to change photo</Text>
            </View>

            {/* Form Fields */}
            <View className="space-y-4">
              <View>
                <Text className="text-gray-700 font-medium mb-2">
                  Full Name
                </Text>
                <TextInput
                  value={editData.full_name}
                  onChangeText={(text) =>
                    setEditData((prev) => ({ ...prev, full_name: text }))
                  }
                  placeholder="Enter your full name"
                  className="bg-gray-50 p-4 rounded-xl text-gray-900"
                />
              </View>

              <View>
                <Text className="text-gray-700 font-medium mb-2">Username</Text>
                <TextInput
                  value={editData.username}
                  onChangeText={(text) =>
                    setEditData((prev) => ({ ...prev, username: text }))
                  }
                  placeholder="Enter your username"
                  className="bg-gray-50 p-4 rounded-xl text-gray-900"
                />
              </View>

              <View>
                <Text className="text-gray-700 font-medium mb-2">About</Text>
                <TextInput
                  value={editData.about}
                  onChangeText={(text) =>
                    setEditData((prev) => ({ ...prev, about: text }))
                  }
                  placeholder="Tell us about yourself"
                  multiline
                  numberOfLines={4}
                  className="bg-gray-50 p-4 rounded-xl text-gray-900"
                />
              </View>

              <View>
                <Text className="text-gray-700 font-medium mb-2">Email</Text>
                <TextInput
                  value={user?.email || ""}
                  editable={false}
                  className="bg-gray-100 p-4 rounded-xl text-gray-500"
                />
              </View>

              {/* Read-only display fields */}
              <View className="mt-6 p-4 bg-gray-50 rounded-xl">
                <Text className="text-gray-600 text-sm mb-2">
                  Additional profile information (display only):
                </Text>
                <Text className="text-gray-700">
                  Gender: {profile?.gender || "Not specified"}
                </Text>
                <Text className="text-gray-700">
                  Age: {profile?.age || "Not specified"}
                </Text>
              </View>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </ScrollView>
  );
};

export default Profile;
