import React, { useState } from "react";
import { View, Text, TouchableOpacity, ActivityIndicator } from "react-native";
import { runCompleteProfileUpdate, verifyProfileStructure } from "../scripts/updateProfiles";
import { toast } from "../lib/toast";

/**
 * Component to handle database migration for profile fields
 * This can be used by administrators or during app initialization
 */

interface DatabaseMigrationProps {
  onMigrationComplete?: () => void;
}

export const DatabaseMigration: React.FC<DatabaseMigrationProps> = ({
  onMigrationComplete,
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [migrationStatus, setMigrationStatus] = useState<string | null>(null);

  const handleRunMigration = async () => {
    setIsRunning(true);
    setMigrationStatus("Starting migration...");

    try {
      await runCompleteProfileUpdate();
      setMigrationStatus("Migration completed successfully!");
      toast.success("Database migration completed successfully!");
      
      if (onMigrationComplete) {
        onMigrationComplete();
      }
    } catch (error) {
      console.error("Migration error:", error);
      setMigrationStatus("Migration failed. Check console for details.");
      toast.error("Migration failed. Please try again.");
    } finally {
      setIsRunning(false);
    }
  };

  const handleVerifyStructure = async () => {
    setIsVerifying(true);
    setMigrationStatus("Verifying database structure...");

    try {
      const isValid = await verifyProfileStructure();
      
      if (isValid) {
        setMigrationStatus("Database structure is valid ✓");
        toast.success("Database structure is valid!");
      } else {
        setMigrationStatus("Database structure needs updates");
        toast.warning("Database structure needs updates");
      }
    } catch (error) {
      console.error("Verification error:", error);
      setMigrationStatus("Verification failed. Check console for details.");
      toast.error("Verification failed. Please try again.");
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <View className="p-6 bg-white rounded-lg shadow-md m-4">
      <Text className="text-xl font-bold text-gray-800 mb-4">
        Database Migration
      </Text>
      
      <Text className="text-gray-600 mb-6">
        This tool helps ensure all profile records have the required fields:
        avatar_url, about, location_lat, location_lng, sexual_orientation, and looking_for.
      </Text>

      <View className="space-y-4">
        {/* Verify Structure Button */}
        <TouchableOpacity
          onPress={handleVerifyStructure}
          disabled={isVerifying || isRunning}
          className={`p-4 rounded-lg ${
            isVerifying || isRunning
              ? "bg-gray-300"
              : "bg-blue-500"
          }`}
        >
          <View className="flex-row items-center justify-center">
            {isVerifying && (
              <ActivityIndicator size="small" color="white" className="mr-2" />
            )}
            <Text className="text-white font-semibold text-center">
              {isVerifying ? "Verifying..." : "Verify Database Structure"}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Run Migration Button */}
        <TouchableOpacity
          onPress={handleRunMigration}
          disabled={isRunning || isVerifying}
          className={`p-4 rounded-lg ${
            isRunning || isVerifying
              ? "bg-gray-300"
              : "bg-green-500"
          }`}
        >
          <View className="flex-row items-center justify-center">
            {isRunning && (
              <ActivityIndicator size="small" color="white" className="mr-2" />
            )}
            <Text className="text-white font-semibold text-center">
              {isRunning ? "Running Migration..." : "Run Complete Migration"}
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Status Display */}
      {migrationStatus && (
        <View className="mt-6 p-4 bg-gray-100 rounded-lg">
          <Text className="text-sm text-gray-700 font-medium">Status:</Text>
          <Text className="text-sm text-gray-600 mt-1">{migrationStatus}</Text>
        </View>
      )}

      {/* Instructions */}
      <View className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
        <Text className="text-sm font-medium text-yellow-800 mb-2">
          Instructions:
        </Text>
        <Text className="text-xs text-yellow-700">
          1. First, click "Verify Database Structure" to check current status{"\n"}
          2. If issues are found, click "Run Complete Migration" to fix them{"\n"}
          3. The migration will update all existing profiles to include required fields
        </Text>
      </View>
    </View>
  );
};

export default DatabaseMigration;
