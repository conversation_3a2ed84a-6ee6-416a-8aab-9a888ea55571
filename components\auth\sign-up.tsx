import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  AppState,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { supabase } from "../../lib/supabase";

// Tells Supabase Auth to continuously refresh the session automatically if
// the app is in the foreground. When this is added, you will continue to receive
// `onAuthStateChange` events with the `TOKEN_REFRESHED` or `SIGNED_OUT` event
// if the user's session is terminated. This should only be registered once.
AppState.addEventListener("change", (state) => {
  if (state === "active") {
    supabase.auth.startAutoRefresh();
  } else {
    supabase.auth.stopAutoRefresh();
  }
});

interface FormData {
  email: string;
}

interface FormError {
  email?: string;
  general?: string;
}

const SignUpCom = ({
  onSignUp,
  onSocialLogin,
  loading,
}: {
  onSignUp: (email: string) => Promise<void>;
  onSocialLogin: (provider: string) => Promise<void>;
  loading: boolean;
}) => {
  const [formData, setFormData] = useState<FormData>({
    email: "",
  });
  const [errors, setErrors] = useState<FormError>({});

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormError = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignUp = () => {
    if (validateForm()) {
      onSignUp(formData.email);
    }
  };

  const renderInputField = (
    label: string,
    field: keyof FormData,
    placeholder: string
  ) => {
    return (
      <View className="mb-5">
        <Text className="text-gray-700 font-medium mb-2 ml-1">{label}</Text>
        <View
          className={`bg-white rounded-xl border ${
            errors[field] ? "border-red-500" : "border-gray-200"
          } px-4 py-3 shadow-sm flex-row items-center`}
        >
          <TextInput
            value={formData[field]}
            onChangeText={(value) => handleInputChange(field, value)}
            placeholder={placeholder}
            autoCapitalize="none"
            autoComplete="email"
            className="flex-1 text-base text-gray-900"
            placeholderTextColor="#9CA3AF"
          />
        </View>
        {errors[field] && (
          <Text className="text-red-500 text-xs ml-1 mt-1">
            {errors[field]}
          </Text>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-white pt-10">
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        className="flex-1"
      >
        <ScrollView
          className="flex-1 px-5"
          contentContainerStyle={{ paddingTop: 32, paddingBottom: 24 }}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View className="flex-1 mb-8">
            <Text className="text-3xl font-bold text-gray-900 mb-2">
              Create Your Account
            </Text>
            <Text className="text-gray-500 text-base">
              We'll send you a one-time password to get started
            </Text>
          </View>

          {/* Form Fields */}
          <View className="mb-2">
            {renderInputField("Email Address", "email", "Enter your email")}
          </View>

          {/* Sign Up Button */}
          <TouchableOpacity
            className="mb-8 rounded-full bg-[#E94057] p-4 mt-4"
            onPress={handleSignUp}
            activeOpacity={0.8}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#ffffff" />
            ) : (
              <Text className="text-white text-lg font-semibold text-center">
                Send OTP
              </Text>
            )}
          </TouchableOpacity>

          {/* Divider */}
          <View className="flex-row items-center mb-8">
            <View className="flex-1 h-px bg-gray-200" />
            <Text className="mx-4 text-gray-400 text-sm">or sign up with</Text>
            <View className="flex-1 h-px bg-gray-200" />
          </View>

          {/* Social Login */}
          <TouchableOpacity
            className="flex-row items-center justify-center bg-white border border-gray-200 rounded-xl py-3 px-4 shadow-sm mb-8"
            onPress={() => onSocialLogin("google")}
            activeOpacity={0.7}
            disabled={loading}
          >
            <Ionicons name="logo-google" size={22} color="#EA4335" />
            <Text className="ml-2 font-medium text-gray-700">
              Continue with Google
            </Text>
          </TouchableOpacity>

          {/* Terms */}
          <Text className="text-gray-500 text-sm text-center mb-8">
            By signing up, you agree to our{" "}
            <Text className="text-pink-500">Terms of use</Text> and{" "}
            <Text className="text-pink-500">Privacy Policy</Text>
          </Text>

          {/* Sign In Link */}
          <View className="flex-row justify-center">
            <Text className="text-gray-600">Already have an account? </Text>
            <TouchableOpacity onPress={() => router.push("/(auth)/sign-in")}>
              <Text className="text-pink-500 font-medium">Sign In</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default SignUpCom;
