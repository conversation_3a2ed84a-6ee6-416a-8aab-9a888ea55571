import { OnboardingData } from "@/types/userdetails";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

interface OnboardingState {
  hasSeenWelcome: boolean;
  hasCompletedProfile: boolean;
  currentStep: number;
  onboardingData: OnboardingData;

  // Actions
  setHasSeenWelcome: (seen: boolean) => void;
  setHasCompletedProfile: (completed: boolean) => void;
  setStep: (step: number) => void;
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  previousStep: () => void;
  updateOnboardingData: (data: Partial<OnboardingData>) => void;
  resetData: () => void;
}

const initialOnboardingData: OnboardingData = {
  profile: {
    full_name: "",
    username: "",
    gender: "",
    age: null,
    about: "",
    sexual_orientation: "",
    looking_for: "",
  },
  interests: [],
  relationshipPreferences: [],
  photos: [],
  discoveryPreferences: {
    min_age: 18,
    max_age: 25,
    max_distance: 50,
    preferred_genders: [],
    show_me: "all",
    deal_breakers: [],
    must_haves: [],
  },
};

export const useOnboardingStore = create<OnboardingState>()(
  persist(
    (set, get) => ({
      hasSeenWelcome: false,
      hasCompletedProfile: false,
      currentStep: 1,
      onboardingData: initialOnboardingData,

      setHasSeenWelcome: (seen) => set({ hasSeenWelcome: seen }),
      setHasCompletedProfile: (completed) =>
        set({ hasCompletedProfile: completed }),
      setStep: (step) => set({ currentStep: step }),
      setCurrentStep: (step) => set({ currentStep: step }),
      nextStep: () => set((state) => ({ currentStep: state.currentStep + 1 })),
      previousStep: () =>
        set((state) => ({ currentStep: Math.max(1, state.currentStep - 1) })),

      updateOnboardingData: (data) => {
        const { onboardingData } = get();
        set({
          onboardingData: {
            ...onboardingData,
            ...data,
            profile: { ...onboardingData.profile, ...data.profile },
            discoveryPreferences: {
              ...onboardingData.discoveryPreferences,
              ...data.discoveryPreferences,
            },
          },
        });
      },

      resetData: () =>
        set({
          currentStep: 1,
          hasCompletedProfile: false,
          onboardingData: initialOnboardingData,
        }),
    }),
    {
      name: "onboarding-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
