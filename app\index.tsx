import { useAuthStore } from "@/stores/authStore";
import { useOnboardingStore } from "@/stores/onboardingStore";
import { Redirect } from "expo-router";
import { ActivityIndicator, View } from "react-native";

export default function Index() {
  const { isAuthenticated, loading, isProfileComplete } = useAuthStore();
  const { hasSeenWelcome } = useOnboardingStore();

  // Show loading state if needed
  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-pink-50">
        <ActivityIndicator size="large" color="#FF6B8A" />
      </View>
    );
  }

  // Redirect based on authentication and profile completion status
  if (isAuthenticated) {
    if (!isProfileComplete) {
      return <Redirect href="/(onboarding)/profile-setup" />;
    }
    return <Redirect href="/(root)/(tabs)/main" />;
  } else {
    if (!hasSeenWelcome) {
      return <Redirect href="/(auth)/welcome" />;
    }
    return <Redirect href="/(auth)/sign-in" />;
  }
}
