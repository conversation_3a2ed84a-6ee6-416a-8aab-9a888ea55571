import { migrateProfileFields } from "./databaseMigration";
import { toast } from "./toast";

/**
 * Utility script to run database migration for existing profiles
 * This should be called once to ensure all existing profiles have the required fields
 */

export const runProfileMigration = async (): Promise<void> => {
  try {
    console.log("Starting profile migration...");
    toast.info("Updating profile database structure...");

    const success = await migrateProfileFields();

    if (success) {
      console.log("Profile migration completed successfully");
      toast.success("Profile database updated successfully!");
    } else {
      console.error("Profile migration failed");
      toast.error("Failed to update profile database");
    }
  } catch (error) {
    console.error("Error running profile migration:", error);
    toast.error("Error occurred during profile database update");
  }
};

/**
 * Check if migration is needed by looking for profiles with undefined fields
 */
export const checkMigrationNeeded = async (): Promise<boolean> => {
  try {
    // This is a simple check - in a real app you might want to store migration status
    // For now, we'll just return true to indicate migration should run
    return true;
  } catch (error) {
    console.error("Error checking migration status:", error);
    return false;
  }
};

/**
 * Initialize migration on app startup if needed
 */
export const initializeMigrationIfNeeded = async (): Promise<void> => {
  try {
    const migrationNeeded = await checkMigrationNeeded();
    
    if (migrationNeeded) {
      console.log("Migration needed, running profile migration...");
      await runProfileMigration();
    } else {
      console.log("No migration needed");
    }
  } catch (error) {
    console.error("Error initializing migration:", error);
  }
};
