import { Ionicons } from "@expo/vector-icons";
import React from "react";
import {
  Image,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const Matches = () => {
  // Mock matches data
  const matches = [
    {
      id: "1",
      name: "<PERSON>",
      age: 25,
      avatar:
        "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400",
      lastMessage: "Hey! How's your day going?",
      timestamp: "2 min ago",
      unread: true,
    },
    {
      id: "2",
      name: "<PERSON>",
      age: 23,
      avatar:
        "https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400",
      lastMessage: "Thanks for the match! 😊",
      timestamp: "1 hour ago",
      unread: false,
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-pink-50">
      <StatusBar barStyle="dark-content" backgroundColor="#fdf2f8" />

      {/* Header */}
      <View className="px-6 py-4 bg-white border-b border-gray-100">
        <Text className="text-2xl font-bold text-gray-800">Matches</Text>
        <Text className="text-gray-600 mt-1">{matches.length} new matches</Text>
      </View>

      <ScrollView className="flex-1">
        {matches.length === 0 ? (
          <View className="flex-1 justify-center items-center p-6 mt-20">
            <Ionicons name="heart-outline" size={80} color="#FF6B8A" />
            <Text className="text-2xl font-bold text-gray-800 mt-4 text-center">
              No matches yet
            </Text>
            <Text className="text-gray-600 mt-2 text-center">
              Keep swiping to find your perfect match!
            </Text>
          </View>
        ) : (
          <View className="p-4">
            {matches.map((match) => (
              <TouchableOpacity
                key={match.id}
                className="bg-white rounded-2xl p-4 mb-4 shadow-sm border border-gray-100 flex-row items-center"
              >
                {/* Avatar */}
                <View className="relative">
                  <Image
                    source={{ uri: match.avatar }}
                    className="w-16 h-16 rounded-full"
                  />
                  {match.unread && (
                    <View className="absolute -top-1 -right-1 w-4 h-4 bg-pink-500 rounded-full border-2 border-white" />
                  )}
                </View>

                {/* Content */}
                <View className="flex-1 ml-4">
                  <View className="flex-row items-center justify-between mb-1">
                    <Text className="text-lg font-semibold text-gray-800">
                      {match.name}, {match.age}
                    </Text>
                    <Text className="text-sm text-gray-500">
                      {match.timestamp}
                    </Text>
                  </View>

                  <Text
                    className={`text-sm ${
                      match.unread
                        ? "text-gray-800 font-medium"
                        : "text-gray-600"
                    }`}
                    numberOfLines={1}
                  >
                    {match.lastMessage}
                  </Text>
                </View>

                {/* Arrow */}
                <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default Matches;
