import { useAppStore } from "@/stores/appStore";
import { useOnboardingStore } from "@/stores/onboardingStore";
import { Ionicons } from "@expo/vector-icons";
import { BlurView } from "expo-blur";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  Dimensions,
  Image,
  ScrollView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

interface OnboardingSlide {
  id: number;
  image: string;
  title: string;
  description: string;
  icon: string;
  accentColor: string;
}

const onboardingData: OnboardingSlide[] = [
  {
    id: 1,
    image:
      "https://images.unsplash.com/photo-1494790108755-2616c2e9d82c?w=400&h=600&fit=crop&crop=face",
    title: "Premium",
    description:
      "Sign up today and enjoy the first month of premium benefits on us.",
    icon: "diamond",
    accentColor: "#FF6B8A",
  },
  {
    id: 2,
    image:
      "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=600&fit=crop&crop=face",
    title: "Matches",
    description:
      "We match you with people that have a large array of similar interests.",
    icon: "heart",
    accentColor: "#FF6B8A",
  },
  {
    id: 3,
    image:
      "https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=600&fit=crop&crop=face",
    title: "Algorithm",
    description:
      "Users going through a vetting process to ensure you never match with bots.",
    icon: "shield-checkmark",
    accentColor: "#FF6B8A",
  },
];

export default function WelcomeScreen() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { isAutoPlaying, setAutoPlaying } = useAppStore();
  const { setHasSeenWelcome } = useOnboardingStore();
  const scrollViewRef = useRef<ScrollView>(null);

  // Animated values
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;

  // Floating animation for images
  const floatingAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Mark welcome screen as seen when component mounts
    setHasSeenWelcome(true);

    // Start floating animation
    const floatingAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(floatingAnim, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(floatingAnim, {
          toValue: 0,
          duration: 3000,
          useNativeDriver: true,
        }),
      ])
    );
    floatingAnimation.start();

    // Auto-play functionality
    let autoPlayInterval: number;
    if (isAutoPlaying) {
      autoPlayInterval = setInterval(() => {
        setCurrentIndex((prevIndex) => {
          const nextIndex = (prevIndex + 1) % onboardingData.length;
          scrollViewRef.current?.scrollTo({
            x: nextIndex * screenWidth,
            animated: true,
          });
          return nextIndex;
        });
      }, 4000);
    }

    return () => {
      if (autoPlayInterval) clearInterval(autoPlayInterval);
      floatingAnimation.stop();
    };
  }, [isAutoPlaying]);

  const handleScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const viewSize = event.nativeEvent.layoutMeasurement;
    const pageIndex = Math.floor(contentOffset.x / viewSize.width);

    if (pageIndex !== currentIndex) {
      setCurrentIndex(pageIndex);

      // Animate content change
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 0.7,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const handleButtonPress = (callback: () => void) => {
    Animated.sequence([
      Animated.timing(buttonScaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonScaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    setTimeout(callback, 200);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    scrollViewRef.current?.scrollTo({
      x: index * screenWidth,
      animated: true,
    });
  };

  const renderFloatingElements = () => (
    <View className="absolute inset-0 pointer-events-none">
      {/* Floating hearts */}
      <Animated.View
        className="absolute top-20 left-10"
        style={{
          transform: [
            {
              translateY: floatingAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -20],
              }),
            },
          ],
          opacity: floatingAnim.interpolate({
            inputRange: [0, 0.5, 1],
            outputRange: [0.3, 0.7, 0.3],
          }),
        }}
      >
        <Ionicons name="heart" size={20} color="#FF6B8A" />
      </Animated.View>

      <Animated.View
        className="absolute top-32 right-8"
        style={{
          transform: [
            {
              translateY: floatingAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 15],
              }),
            },
          ],
          opacity: floatingAnim.interpolate({
            inputRange: [0, 0.5, 1],
            outputRange: [0.2, 0.6, 0.2],
          }),
        }}
      >
        <Ionicons name="star" size={16} color="#FFD700" />
      </Animated.View>

      <Animated.View
        className="absolute bottom-40 left-6"
        style={{
          transform: [
            {
              translateY: floatingAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -10],
              }),
            },
          ],
          opacity: floatingAnim.interpolate({
            inputRange: [0, 0.5, 1],
            outputRange: [0.4, 0.8, 0.4],
          }),
        }}
      >
        <Ionicons name="diamond" size={18} color="#8A8A8A" />
      </Animated.View>
    </View>
  );

  const renderSlide = (item: OnboardingSlide, index: number) => (
    <View
      key={item.id}
      className="flex-1"
      style={{
        width: screenWidth,
      }}
    >
      {/* Content Container */}
      <View className="flex-1 items-center justify-center px-6 pt-12 pb-4">
        {/* Profile Image Container */}
        <Animated.View
          className="relative mb-8"
          style={{
            opacity: fadeAnim,
          }}
        >
          {/* Side Images (Partially Visible) - Fixed positioning */}
          {index > 0 && (
            <View
              className="absolute left-0 top-0 w-16 h-60 rounded-2xl overflow-hidden opacity-30"
              style={{
                left: -screenWidth * 0.12,
                zIndex: 1,
                borderColor: "#FF6B8A40",
                borderWidth: 1,
              }}
            >
              <Image
                source={{ uri: onboardingData[index - 1].image }}
                className="w-full h-full"
                resizeMode="cover"
              />
              <BlurView intensity={15} className="absolute inset-0" />
            </View>
          )}

          {index < onboardingData.length - 1 && (
            <View
              className="absolute right-0 top-0 w-16 h-60 rounded-2xl overflow-hidden opacity-30"
              style={{
                right: -screenWidth * 0.12,
                zIndex: 1,
                borderColor: "#FF6B8A40",
                borderWidth: 1,
              }}
            >
              <Image
                source={{ uri: onboardingData[index + 1].image }}
                className="w-full h-full"
                resizeMode="cover"
              />
              <BlurView intensity={15} className="absolute inset-0" />
            </View>
          )}

          {/* Main Image with Enhanced Pink Effects - Fixed sizing */}
          <Animated.View
            className="rounded-3xl overflow-hidden shadow-2xl"
            style={{
              width: screenWidth * 0.6,
              height: screenHeight * 0.4,
              zIndex: 2,
              transform: [
                {
                  translateY: floatingAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, -8],
                  }),
                },
              ],
              backgroundColor: "#FECFEF40", // Consistent pink background for image container
            }}
          >
            <Image
              source={{ uri: item.image }}
              style={{
                width: "100%",
                height: "100%",
              }}
              resizeMode="cover"
            />

            {/* Pink Overlay for consistent look */}
            <LinearGradient
              colors={["transparent", "#FF9A8B30", "#FF6B8A20"]}
              className="absolute inset-0"
            />

            {/* Shimmer Effect - Simplified */}
            <Animated.View
              className="absolute inset-0"
              style={{
                opacity: floatingAnim.interpolate({
                  inputRange: [0, 0.5, 1],
                  outputRange: [0, 0.2, 0],
                }),
              }}
            >
              <LinearGradient
                colors={["transparent", "#FECFEF50", "transparent"]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                className="absolute inset-0"
              />
            </Animated.View>

            {/* Icon Badge - Enhanced with consistent pink */}
            <View className="absolute top-4 right-4">
              <BlurView intensity={80} className="rounded-full p-2">
                <Ionicons name={item.icon as any} size={20} color="#FF6B8A" />
              </BlurView>
            </View>
          </Animated.View>

          {/* Decorative Ring - Simplified with consistent pink */}
          <Animated.View
            className="absolute -inset-3 rounded-3xl border border-opacity-40"
            style={{
              borderColor: "#FF6B8A",
              width: screenWidth * 0.6 + 24,
              height: screenHeight * 0.4 + 24,
              left: -12,
              top: -12,
              zIndex: 1,
              transform: [
                {
                  scale: floatingAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 1.02],
                  }),
                },
              ],
            }}
          />
        </Animated.View>

        {/* Enhanced Content - Simplified */}
        <Animated.View
          className="items-center px-4 w-full max-w-sm"
          style={{
            opacity: fadeAnim,
          }}
        >
          <Text
            className="text-3xl font-black mb-3 text-center"
            style={{ color: "#FF6B8A" }}
          >
            {item.title}
          </Text>
          <Text className="text-base text-gray-600 text-center leading-6 font-medium max-w-xs">
            {item.description}
          </Text>

          {/* Feature Highlights - Enhanced with pink theme */}
          <View className="flex-row mt-6 space-x-4">
            {["✨", "🎯", "🔥"].map((emoji, idx) => (
              <Animated.View
                key={idx}
                className="w-8 h-8 rounded-full bg-white shadow-md items-center justify-center"
                style={{
                  transform: [
                    {
                      scale: floatingAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [1, 1.05],
                      }),
                    },
                  ],
                  borderWidth: 1,
                  borderColor: "#FF9A8B30",
                }}
              >
                <Text className="text-sm">{emoji}</Text>
              </Animated.View>
            ))}
          </View>
        </Animated.View>
      </View>
    </View>
  );

  // Update pagination for consistent pink theme
  const renderSimplePagination = () => (
    <View className="mb-8">
      {/* Simple Dots Only */}
      <View className="flex-row justify-center items-center">
        {onboardingData.map((_, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => goToSlide(index)}
            className="mx-2 p-2"
          >
            <Animated.View
              className={`rounded-full ${
                index === currentIndex ? "w-8 h-3" : "w-3 h-3"
              }`}
              style={{
                backgroundColor:
                  index === currentIndex ? "#FF6B8A" : "#FECFEF80",
                transform: [
                  {
                    scale: index === currentIndex ? 1.1 : 1,
                  },
                ],
              }}
            />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  // Update buttons with pink theme
  const renderEnhancedButtons = () => (
    <View className="px-6 pb-6">
      {/* Auto-play Toggle - Styled with pink */}
      <TouchableOpacity
        onPress={() => setAutoPlaying(!isAutoPlaying)}
        className="self-center mb-4"
      >
        <View className="flex-row items-center">
          <Ionicons
            name={isAutoPlaying ? "pause" : "play"}
            size={12}
            color="#FF9A8B"
          />
          <Text className="ml-1 text-xs" style={{ color: "#FF9A8B" }}>
            {isAutoPlaying ? "Auto" : "Manual"}
          </Text>
        </View>
      </TouchableOpacity>

      {/* Enhanced Create Account Button - Pink theme */}
      <Animated.View style={{ transform: [{ scale: buttonScaleAnim }] }}>
        <TouchableOpacity
          onPress={() =>
            handleButtonPress(() => router.push("/(auth)/sign-up"))
          }
          className="rounded-2xl overflow-hidden shadow-xl mb-4"
        >
          <LinearGradient
            colors={["#FF9A8B", "#FF6B8A", "#FF4D6D"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            className="py-4"
          >
            <View className="flex-row items-center justify-center">
              <Ionicons name="person-add" size={18} color="white" />
              <Text className="text-white text-center text-lg font-bold ml-2">
                Create an account
              </Text>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>

      {/* Enhanced Sign In Link - Pink theme */}
      <View className="flex-row justify-center items-center mb-4">
        <Text className="text-gray-600 text-base">
          Already have an account?{" "}
        </Text>
        <TouchableOpacity
          onPress={() =>
            handleButtonPress(() => router.push("/(auth)/sign-in"))
          }
          className="flex-row items-center"
        >
          <Text
            className="text-base font-semibold mr-1"
            style={{ color: "#FF6B8A" }}
          >
            Sign In
          </Text>
          <Ionicons name="arrow-forward" size={14} color="#FF6B8A" />
        </TouchableOpacity>
      </View>

      {/* Social Login Options - With subtle pink border */}
      <View>
        <View className="flex-row items-center mb-4">
          <View
            className="flex-1 h-px"
            style={{ backgroundColor: "#FECFEF" }}
          />
          <Text className="mx-4 text-gray-500 text-sm">or continue with</Text>
          <View
            className="flex-1 h-px"
            style={{ backgroundColor: "#FECFEF" }}
          />
        </View>

        <View className="flex-row justify-center space-x-4">
          {[
            { name: "logo-google", color: "#4285F4" },
            { name: "logo-apple", color: "#000000" },
            { name: "logo-facebook", color: "#1877F2" },
          ].map((social, index) => (
            <TouchableOpacity
              key={index}
              onPress={() =>
                handleButtonPress(() => console.log(`${social.name} login`))
              }
              className="w-12 h-12 rounded-xl bg-white shadow-md items-center justify-center"
              style={{ borderWidth: 1, borderColor: "#FECFEF50" }}
            >
              <Ionicons
                name={social.name as any}
                size={22}
                color={social.color}
              />
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView
      className="flex-1"
      style={{
        backgroundColor: "#FF9A8B20",
      }}
    >
      <StatusBar barStyle="dark-content" backgroundColor="#FF9A8B20" />

      {/* Floating Elements */}
      {renderFloatingElements()}

      <View className="flex-1">
        {/* Enhanced Carousel */}
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={handleScroll}
          onScrollBeginDrag={() => setAutoPlaying(false)}
          className="flex-1"
          decelerationRate="fast"
          snapToInterval={screenWidth}
          snapToAlignment="start"
        >
          {onboardingData.map((item, index) => renderSlide(item, index))}
        </ScrollView>

        {/* Bottom Section */}
        <View className="bg-transparent mb-2">
          {renderSimplePagination()}
          {renderEnhancedButtons()}
        </View>
      </View>
    </SafeAreaView>
  );
}
