export interface ProfileData {
  id: string;
  updated_at: string | null;
  username: string | null;
  full_name: string | null;
  avatar_url: string | null;
  gender: string | null;
  age: number | null;
  about: string | null;
  is_first_time: boolean;
  location_lat: number | null;
  location_lng: number | null;
  notification_enabled: boolean;
  sexual_orientation: string | null;
  looking_for: string | null;
  last_active_at: string | null;
  profile_complete: boolean;
  visibility_status: string | null;
}

export interface Interest {
  id: number;
  name: string;
}

export interface UserInterest {
  id: number;
  user_id: string;
  interest_id: number;
  interest?: Interest;
}

export interface UserPhoto {
  id: number;
  user_id: string;
  photo_url: string;
  created_at: string;
  is_profile_photo: boolean;
}

export interface RelationshipPreference {
  id: number;
  name: string;
}

export interface UserRelationshipPreference {
  id: number;
  user_id: string;
  preference_id: number;
  preference?: RelationshipPreference;
}

export interface DiscoveryPreferences {
  id: number;
  user_id: string;
  min_age: number;
  max_age: number;
  max_distance: number;
  preferred_genders: string[];
  show_me: string;
  deal_breakers: string[];
  must_haves: string[];
  updated_at: string;
}

export interface OnboardingData {
  profile: Partial<ProfileData>;
  interests: number[];
  relationshipPreferences: number[];
  photos: string[];
  discoveryPreferences: Partial<DiscoveryPreferences>;
}
