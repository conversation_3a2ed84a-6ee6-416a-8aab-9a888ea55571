import { create } from "zustand";

interface AppState {
  isAutoPlaying: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  setAutoPlaying: (isPlaying: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useAppStore = create<AppState>((set) => ({
  isAutoPlaying: true,
  isLoading: false,
  error: null,

  setAutoPlaying: (isPlaying) => set({ isAutoPlaying: isPlaying }),
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  clearError: () => set({ error: null }),
}));
