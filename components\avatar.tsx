import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import { LinearGradient } from "expo-linear-gradient";
import { useEffect, useState } from "react";
import { Alert, Image, Text, TouchableOpacity, View } from "react-native";
import { supabase } from "../lib/supabase";

interface Props {
  size: number;
  url: string | null;
  onUpload: (filePath: string) => void;
}

export default function Avatar({ url, size = 150, onUpload }: Props) {
  const [uploading, setUploading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const avatarSize = { height: size, width: size };

  useEffect(() => {
    if (url) downloadImage(url);
  }, [url]);

  async function downloadImage(path: string) {
    try {
      const { data, error } = await supabase.storage
        .from("avatars")
        .download(path);

      if (error) {
        throw error;
      }

      const fr = new FileReader();
      fr.readAsDataURL(data);
      fr.onload = () => {
        setAvatarUrl(fr.result as string);
      };
    } catch (error) {
      if (error instanceof Error) {
        console.log("Error downloading image: ", error.message);
      }
    }
  }

  async function uploadAvatar() {
    try {
      setUploading(true);

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: false,
        allowsEditing: true,
        quality: 0.8, // Slightly reduced for better performance
        aspect: [1, 1], // Ensure square images
        exif: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.log("User cancelled image picker.");
        return;
      }

      const image = result.assets[0];
      console.log("Got image", image);

      if (!image.uri) {
        throw new Error("No image uri!");
      }

      const arraybuffer = await fetch(image.uri).then((res) =>
        res.arrayBuffer()
      );

      const fileExt = image.uri?.split(".").pop()?.toLowerCase() ?? "jpeg";
      const path = `${Date.now()}.${fileExt}`;
      const { data, error: uploadError } = await supabase.storage
        .from("avatars")
        .upload(path, arraybuffer, {
          contentType: image.mimeType ?? "image/jpeg",
        });

      if (uploadError) {
        throw uploadError;
      }

      onUpload(data.path);
    } catch (error) {
      if (error instanceof Error) {
        Alert.alert("Upload Error", error.message);
      } else {
        throw error;
      }
    } finally {
      setUploading(false);
    }
  }

  return (
    <View className="items-center">
      <View className="relative">
        {avatarUrl ? (
          <>
            <Image
              source={{ uri: avatarUrl }}
              accessibilityLabel="Avatar"
              className="rounded-full overflow-hidden"
              style={avatarSize}
            />
            <LinearGradient
              colors={["rgba(255,107,138,0.1)", "rgba(255,154,139,0.3)"]}
              className="absolute top-0 left-0 right-0 bottom-0 rounded-full border-2 border-pink-500"
              style={avatarSize}
            />
          </>
        ) : (
          <View
            className="rounded-full border-2 border-pink-300 bg-pink-50 items-center justify-center overflow-hidden"
            style={avatarSize}
          >
            <Ionicons name="person" size={size * 0.5} color="#FF9A8B" />
          </View>
        )}

        <TouchableOpacity
          className="absolute bottom-0 right-0 rounded-full overflow-hidden border-2 border-white"
          onPress={uploadAvatar}
          disabled={uploading}
        >
          <LinearGradient
            colors={["#FF9A8B", "#FF6B8A"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            className="w-9 h-9 items-center justify-center"
          >
            <Ionicons name="camera" size={size * 0.15} color="white" />
          </LinearGradient>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        className={`flex-row items-center justify-center py-2 px-4 rounded-full mt-1.5 ${
          uploading ? "bg-gray-200" : "bg-pink-50"
        }`}
        onPress={uploadAvatar}
        disabled={uploading}
      >
        <Text className="text-pink-500 font-medium">
          {uploading ? "Uploading..." : "Change Photo"}
        </Text>
        {!uploading && (
          <Ionicons
            name="image-outline"
            size={16}
            color="#FF6B8A"
            style={{ marginLeft: 4 }}
          />
        )}
      </TouchableOpacity>
    </View>
  );
}
