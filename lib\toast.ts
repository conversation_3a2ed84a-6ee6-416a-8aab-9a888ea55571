import Toast from "react-native-toast-message";

export interface ToastOptions {
  text1?: string;
  text2?: string;
  position?: "top" | "bottom";
  visibilityTime?: number;
  autoHide?: boolean;
  topOffset?: number;
  bottomOffset?: number;
}

export const showToast = {
  success: (message: string, options?: ToastOptions) => {
    Toast.show({
      type: "success",
      text1: "Success",
      text2: message,
      position: "top",
      visibilityTime: 3000,
      autoHide: true,
      topOffset: 60,
      ...options,
    });
  },

  error: (message: string, options?: ToastOptions) => {
    Toast.show({
      type: "error",
      text1: "Error",
      text2: message,
      position: "top",
      visibilityTime: 4000,
      autoHide: true,
      topOffset: 60,
      ...options,
    });
  },

  info: (message: string, options?: ToastOptions) => {
    Toast.show({
      type: "info",
      text1: "Info",
      text2: message,
      position: "top",
      visibilityTime: 3000,
      autoHide: true,
      topOffset: 60,
      ...options,
    });
  },

  warning: (message: string, options?: ToastOptions) => {
    Toast.show({
      type: "error", // Using error type for warning as it's more visible
      text1: "Warning",
      text2: message,
      position: "top",
      visibilityTime: 4000,
      autoHide: true,
      topOffset: 60,
      ...options,
    });
  },

  custom: (type: string, title: string, message: string, options?: ToastOptions) => {
    Toast.show({
      type: type as any,
      text1: title,
      text2: message,
      position: "top",
      visibilityTime: 3000,
      autoHide: true,
      topOffset: 60,
      ...options,
    });
  },

  hide: () => {
    Toast.hide();
  },
};

// Convenience methods with shorter names
export const toast = showToast;

// Export the Toast component for custom configurations
export { Toast };
