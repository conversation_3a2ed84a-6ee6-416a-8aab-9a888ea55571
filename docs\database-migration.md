# Database Migration Guide

This guide explains how to update the profiles table to include the required fields that were previously null.

## Overview

The profiles table has been updated to ensure all records have the following fields properly initialized:
- `avatar_url` (string | null)
- `about` (string | null) 
- `location_lat` (number | null)
- `location_lng` (number | null)
- `sexual_orientation` (string | null)
- `looking_for` (string | null)

## Migration Files

### 1. `lib/databaseMigration.ts`
Contains the core migration functions:
- `migrateProfileFields()` - Updates existing profiles
- `initializeNewProfile()` - Creates new profiles with all required fields
- `updateProfileField()` - Updates individual fields
- `updateMultipleProfileFields()` - Batch updates multiple fields

### 2. `scripts/updateProfiles.ts`
Standalone script for updating existing profiles:
- `updateExistingProfiles()` - Updates all existing profiles
- `verifyProfileStructure()` - Verifies all profiles have required fields
- `runCompleteProfileUpdate()` - Complete migration process

### 3. `components/DatabaseMigration.tsx`
React component for running migrations from within the app:
- UI for verifying database structure
- UI for running complete migration
- Status display and instructions

## How to Run Migration

### Option 1: Using the React Component (Recommended)

1. Import the component in your app:
```tsx
import DatabaseMigration from "@/components/DatabaseMigration";
```

2. Add it to a screen (e.g., admin panel or settings):
```tsx
<DatabaseMigration onMigrationComplete={() => console.log("Migration done!")} />
```

3. Use the UI to verify and run migration

### Option 2: Programmatically

```tsx
import { runCompleteProfileUpdate } from "@/scripts/updateProfiles";

// Run the complete migration
await runCompleteProfileUpdate();
```

### Option 3: Individual Functions

```tsx
import { migrateProfileFields } from "@/lib/databaseMigration";

// Run just the core migration
const success = await migrateProfileFields();
```

## What the Migration Does

1. **Checks existing profiles** for undefined fields
2. **Sets null values** for missing fields (avatar_url, about, location_lat, location_lng, sexual_orientation, looking_for)
3. **Updates timestamps** to reflect the changes
4. **Provides fallback** methods if the new initialization fails
5. **Logs progress** and any errors encountered

## Updated Profile Creation

All profile creation functions have been updated to use the new `initializeNewProfile()` function:

- `app/(auth)/sign-up.tsx` - Sign up flow
- `app/(auth)/verify-user.tsx` - Email verification
- `stores/authStore.ts` - Auth state management
- `app/(onboarding)/profile-setup.tsx` - Profile setup flow

## Database Schema

The profiles table now ensures all records have these fields:

```sql
CREATE TABLE profiles (
  id uuid PRIMARY KEY,
  username text UNIQUE,
  full_name text,
  avatar_url text,           -- Now properly initialized
  gender text,
  age integer,
  about text,                -- Now properly initialized
  location_lat double precision,  -- Now properly initialized
  location_lng double precision,  -- Now properly initialized
  sexual_orientation text,   -- Now properly initialized
  looking_for text,          -- Now properly initialized
  -- ... other existing fields
);
```

## Verification

After running the migration, you can verify success by:

1. Using the verification function:
```tsx
import { verifyProfileStructure } from "@/scripts/updateProfiles";
const isValid = await verifyProfileStructure();
```

2. Checking the database directly:
```sql
SELECT id, avatar_url, about, location_lat, location_lng, sexual_orientation, looking_for 
FROM profiles 
WHERE avatar_url IS NULL OR about IS NULL OR location_lat IS NULL 
   OR location_lng IS NULL OR sexual_orientation IS NULL OR looking_for IS NULL;
```

## Notes

- The migration is **safe to run multiple times**
- Fields are set to `null` by design - they will be populated when users update their profiles
- All existing profile creation flows have been updated to use the new structure
- The migration includes error handling and fallback methods
- Progress is logged to the console for monitoring

## Troubleshooting

If you encounter issues:

1. Check the console logs for detailed error messages
2. Verify your Supabase connection is working
3. Ensure you have the necessary permissions to update the profiles table
4. Try running the verification function first to identify specific issues

## Future Profile Updates

When users update their profiles, use the helper functions:

```tsx
import { updateProfileField, updateMultipleProfileFields } from "@/lib/databaseMigration";

// Update single field
await updateProfileField(userId, "about", "New bio text");

// Update multiple fields
await updateMultipleProfileFields(userId, {
  about: "New bio",
  sexual_orientation: "heterosexual",
  looking_for: "long-term relationship"
});
```
