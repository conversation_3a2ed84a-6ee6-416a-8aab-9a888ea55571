import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import React from "react";
import {
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const Main = () => {
  const router = useRouter();

  return (
    <SafeAreaView className="flex-1 bg-pink-50">
      <StatusBar barStyle="dark-content" backgroundColor="#fdf2f8" />

      {/* Header */}
      <View className="flex-row justify-between items-center px-6 py-4">
        <TouchableOpacity onPress={() => router.push("/(root)/(tabs)/profile")}>
          <Ionicons name="person-circle-outline" size={32} color="#FF6B8A" />
        </TouchableOpacity>

        <Text className="text-2xl font-bold text-gray-800">Discover</Text>

        <TouchableOpacity onPress={() => router.push("/(root)/(tabs)/matches")}>
          <Ionicons name="chatbubbles-outline" size={32} color="#FF6B8A" />
        </TouchableOpacity>
      </View>

      {/* Main Content */}
      <View className="flex-1 justify-center items-center p-6">
        <Ionicons name="heart-outline" size={80} color="#FF6B8A" />
        <Text className="text-2xl font-bold text-gray-800 mt-4 text-center">
          Welcome to Discover
        </Text>
        <Text className="text-gray-600 mt-2 text-center">
          Start swiping to find your perfect match!
        </Text>

        <TouchableOpacity
          onPress={() => router.push("/(root)/(tabs)/profile")}
          className="mt-6 bg-pink-500 px-6 py-3 rounded-full"
        >
          <Text className="text-white font-semibold">
            Complete Your Profile
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default Main;
