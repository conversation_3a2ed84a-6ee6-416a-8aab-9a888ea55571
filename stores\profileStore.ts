import { supabase } from "@/lib/supabase";
import {
  DiscoveryPreferences,
  Interest,
  ProfileData,
  RelationshipPreference,
  UserInterest,
  UserPhoto,
  UserRelationshipPreference,
} from "@/types/userdetails";
import { create } from "zustand";

interface ProfileState {
  profile: ProfileData | null;
  photos: UserPhoto[];
  interests: Interest[];
  userInterests: UserInterest[];
  relationshipPreferences: RelationshipPreference[];
  userRelationshipPreferences: UserRelationshipPreference[];
  discoveryPreferences: DiscoveryPreferences | null;
  loading: boolean;
  error: string | null;

  // Actions
  fetchProfile: (userId: string) => Promise<void>;
  updateProfile: (updates: Partial<ProfileData>) => Promise<boolean>;
  uploadPhoto: (uri: string, isProfilePhoto?: boolean) => Promise<boolean>;
  deletePhoto: (photoId: number) => Promise<boolean>;
  setProfilePhoto: (photoId: number) => Promise<boolean>;
  fetchInterests: () => Promise<void>;
  updateUserInterests: (interestIds: number[]) => Promise<boolean>;
  fetchRelationshipPreferences: () => Promise<void>;
  updateUserRelationshipPreferences: (
    preferenceIds: number[]
  ) => Promise<boolean>;
  updateDiscoveryPreferences: (
    preferences: Partial<DiscoveryPreferences>
  ) => Promise<boolean>;
  markProfileComplete: () => Promise<boolean>;
  clearProfile: () => void;
}

export const useProfileStore = create<ProfileState>((set, get) => ({
  profile: null,
  photos: [],
  interests: [],
  userInterests: [],
  relationshipPreferences: [],
  userRelationshipPreferences: [],
  discoveryPreferences: null,
  loading: false,
  error: null,

  fetchProfile: async (userId: string) => {
    set({ loading: true, error: null });

    try {
      // Fetch profile
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (profileError && profileError.code !== "PGRST116") {
        throw profileError;
      }

      // Fetch photos
      const { data: photos, error: photosError } = await supabase
        .from("user_photos")
        .select("*")
        .eq("user_id", userId)
        .order("created_at", { ascending: false });

      if (photosError) {
        console.error("Error fetching photos:", photosError);
      }

      // Fetch user interests with interest details
      const { data: userInterests, error: interestsError } = await supabase
        .from("user_interests")
        .select(
          `
          *,
          interest:interests(*)
        `
        )
        .eq("user_id", userId);

      if (interestsError) {
        console.error("Error fetching user interests:", interestsError);
      }

      // Fetch user relationship preferences with preference details
      const { data: userRelPrefs, error: relPrefsError } = await supabase
        .from("user_relationship_preferences")
        .select(
          `
          *,
          preference:relationship_preferences(*)
        `
        )
        .eq("user_id", userId);

      if (relPrefsError) {
        console.error(
          "Error fetching relationship preferences:",
          relPrefsError
        );
      }

      // Fetch discovery preferences
      const { data: discoveryPrefs, error: discoveryError } = await supabase
        .from("discovery_preferences")
        .select("*")
        .eq("user_id", userId)
        .single();

      if (discoveryError && discoveryError.code !== "PGRST116") {
        console.error("Error fetching discovery preferences:", discoveryError);
      }

      set({
        profile: profile || null,
        photos: photos || [],
        userInterests: userInterests || [],
        userRelationshipPreferences: userRelPrefs || [],
        discoveryPreferences: discoveryPrefs || null,
        loading: false,
      });
    } catch (error) {
      console.error("Error fetching profile:", error);
      set({ error: "Failed to fetch profile", loading: false });
    }
  },

  updateProfile: async (updates: Partial<ProfileData>) => {
    const { profile } = get();

    // Get user ID from either the existing profile or the updates
    const userId = profile?.id || updates.id;
    if (!userId) {
      console.error("No user ID available for profile update");
      return false;
    }

    set({ loading: true, error: null });

    try {
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from("profiles")
        .update(updateData)
        .eq("id", userId);

      if (error) {
        console.error("Supabase update error:", error);
        throw error;
      }

      // Update local state - merge with existing profile if available
      const updatedProfile = profile
        ? { ...profile, ...updates }
        : ({ ...updates, id: userId } as ProfileData);

      set({
        profile: updatedProfile,
        loading: false,
      });

      return true;
    } catch (error) {
      console.error("Error updating profile:", error);
      set({ error: "Failed to update profile", loading: false });
      return false;
    }
  },

  uploadPhoto: async (uri: string, isProfilePhoto = false) => {
    const { profile } = get();
    if (!profile) return false;

    set({ loading: true, error: null });

    try {
      // Create a unique filename
      const fileExt = uri.split(".").pop();
      const fileName = `${profile.id}/${Date.now()}.${fileExt}`;

      // Convert URI to blob for upload
      const response = await fetch(uri);
      const blob = await response.blob();

      // Upload to Supabase storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("userphotos")
        .upload(fileName, blob);

      if (uploadError) throw uploadError;

      // Get public URL
      const {
        data: { publicUrl },
      } = supabase.storage.from("userphotos").getPublicUrl(fileName);

      // Save photo record to database
      const { data: photoData, error: photoError } = await supabase
        .from("user_photos")
        .insert({
          user_id: profile.id,
          photo_url: publicUrl,
          is_profile_photo: isProfilePhoto,
        })
        .select()
        .single();

      if (photoError) throw photoError;

      // If this is a profile photo, update the profile avatar_url
      if (isProfilePhoto) {
        await get().updateProfile({ avatar_url: publicUrl });
      }

      // Update local state
      const { photos } = get();
      set({
        photos: [photoData, ...photos],
        loading: false,
      });

      return true;
    } catch (error) {
      console.error("Error uploading photo:", error);
      set({ error: "Failed to upload photo", loading: false });
      return false;
    }
  },

  deletePhoto: async (photoId: number) => {
    set({ loading: true, error: null });

    try {
      const { error } = await supabase
        .from("user_photos")
        .delete()
        .eq("id", photoId);

      if (error) throw error;

      // Update local state
      const { photos } = get();
      set({
        photos: photos.filter((photo) => photo.id !== photoId),
        loading: false,
      });

      return true;
    } catch (error) {
      console.error("Error deleting photo:", error);
      set({ error: "Failed to delete photo", loading: false });
      return false;
    }
  },

  setProfilePhoto: async (photoId: number) => {
    const { photos, profile } = get();
    if (!profile) return false;

    const photo = photos.find((p) => p.id === photoId);
    if (!photo) return false;

    set({ loading: true, error: null });

    try {
      // Update all photos to not be profile photo
      await supabase
        .from("user_photos")
        .update({ is_profile_photo: false })
        .eq("user_id", profile.id);

      // Set the selected photo as profile photo
      const { error } = await supabase
        .from("user_photos")
        .update({ is_profile_photo: true })
        .eq("id", photoId);

      if (error) throw error;

      // Update profile avatar_url
      await get().updateProfile({ avatar_url: photo.photo_url });

      // Update local state
      set({
        photos: photos.map((p) => ({
          ...p,
          is_profile_photo: p.id === photoId,
        })),
        loading: false,
      });

      return true;
    } catch (error) {
      console.error("Error setting profile photo:", error);
      set({ error: "Failed to set profile photo", loading: false });
      return false;
    }
  },

  fetchInterests: async () => {
    try {
      const { data, error } = await supabase
        .from("interests")
        .select("*")
        .order("name");

      if (error) throw error;

      set({ interests: data || [] });
    } catch (error) {
      console.error("Error fetching interests:", error);
    }
  },

  updateUserInterests: async (interestIds: number[]) => {
    const { profile } = get();
    if (!profile) return false;

    set({ loading: true, error: null });

    try {
      // Delete existing interests
      await supabase.from("user_interests").delete().eq("user_id", profile.id);

      // Insert new interests
      if (interestIds.length > 0) {
        const { error } = await supabase.from("user_interests").insert(
          interestIds.map((interestId) => ({
            user_id: profile.id,
            interest_id: interestId,
          }))
        );

        if (error) throw error;
      }

      // Refresh user interests
      await get().fetchProfile(profile.id);

      return true;
    } catch (error) {
      console.error("Error updating user interests:", error);
      set({ error: "Failed to update interests", loading: false });
      return false;
    }
  },

  fetchRelationshipPreferences: async () => {
    try {
      const { data, error } = await supabase
        .from("relationship_preferences")
        .select("*")
        .order("name");

      if (error) throw error;

      set({ relationshipPreferences: data || [] });
    } catch (error) {
      console.error("Error fetching relationship preferences:", error);
    }
  },

  updateUserRelationshipPreferences: async (preferenceIds: number[]) => {
    const { profile } = get();
    if (!profile) return false;

    set({ loading: true, error: null });

    try {
      // Delete existing preferences
      await supabase
        .from("user_relationship_preferences")
        .delete()
        .eq("user_id", profile.id);

      // Insert new preferences
      if (preferenceIds.length > 0) {
        const { error } = await supabase
          .from("user_relationship_preferences")
          .insert(
            preferenceIds.map((preferenceId) => ({
              user_id: profile.id,
              preference_id: preferenceId,
            }))
          );

        if (error) throw error;
      }

      // Refresh user relationship preferences
      await get().fetchProfile(profile.id);

      return true;
    } catch (error) {
      console.error("Error updating relationship preferences:", error);
      set({
        error: "Failed to update relationship preferences",
        loading: false,
      });
      return false;
    }
  },

  updateDiscoveryPreferences: async (
    preferences: Partial<DiscoveryPreferences>
  ) => {
    const { profile, discoveryPreferences } = get();
    if (!profile) return false;

    set({ loading: true, error: null });

    try {
      const updateData = {
        ...preferences,
        user_id: profile.id,
        updated_at: new Date().toISOString(),
      };

      let result;
      if (discoveryPreferences) {
        // Update existing preferences
        result = await supabase
          .from("discovery_preferences")
          .update(updateData)
          .eq("user_id", profile.id)
          .select()
          .single();
      } else {
        // Insert new preferences
        result = await supabase
          .from("discovery_preferences")
          .insert(updateData)
          .select()
          .single();
      }

      if (result.error) throw result.error;

      set({
        discoveryPreferences: result.data,
        loading: false,
      });

      return true;
    } catch (error) {
      console.error("Error updating discovery preferences:", error);
      set({ error: "Failed to update discovery preferences", loading: false });
      return false;
    }
  },

  markProfileComplete: async () => {
    const { profile } = get();
    if (!profile) return false;

    return await get().updateProfile({ profile_complete: true });
  },

  clearProfile: () => {
    set({
      profile: null,
      photos: [],
      userInterests: [],
      userRelationshipPreferences: [],
      discoveryPreferences: null,
      loading: false,
      error: null,
    });
  },
}));
